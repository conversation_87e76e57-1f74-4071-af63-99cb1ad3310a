{"ast": null, "code": "import { isNode } from 'detect-node-es';\nexport var env = {\n  isNode: isNode,\n  forceCache: false\n};", "map": {"version": 3, "names": ["isNode", "env", "forceCache"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/use-sidecar/dist/es2015/env.js"], "sourcesContent": ["import { isNode } from 'detect-node-es';\nexport var env = {\n    isNode: isNode,\n    forceCache: false,\n};\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,gBAAgB;AACvC,OAAO,IAAIC,GAAG,GAAG;EACbD,MAAM,EAAEA,MAAM;EACdE,UAAU,EAAE;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}