{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/PersonalInfo.js\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Badge } from \"../UI/Badge\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"../UI/Card\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PersonalInfo = () => {\n  _s();\n  const [selectedSession, setSelectedSession] = useState(null);\n  const toggleSession = sessionId => {\n    setSelectedSession(sessionId);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"h-full\",\n    children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n      children: /*#__PURE__*/_jsxDEV(CardTitle, {\n        className: \"flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-primary rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/IMAGE/person.png\",\n            alt: \"Person\",\n            className: \"w-full h-full object-contain\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-lg font-semibold\",\n          children: \"Personal Information & Certifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 p-3 bg-accent rounded-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 bg-primary rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/IMAGE/person2.png\",\n            alt: \"Person2\",\n            className: \"w-full h-full object-contain\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-semibold text-foreground\",\n            children: \"Alexander Almutairi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-muted-foreground\",\n            children: \"Consultant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/IMAGE/grad.png\",\n            alt: \"MBA\",\n            className: \"w-10 h-10 object-contain rounded-full border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"MBA - Ewha Womans University, South Korea\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/IMAGE/Group.png\",\n            alt: \"Business\",\n            className: \"w-10 h-10 object-contain rounded-full border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"Business & IT Consultant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/IMAGE/security.png\",\n            alt: \"CEH\",\n            className: \"w-10 h-10 object-contain rounded-full border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"CEH - Certified Ethical Hacker\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/IMAGE/cer4.png\",\n            alt: \"ISO 9001\",\n            className: \"w-10 h-10 object-contain rounded-full border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"ISO 9001 - Quality Management Systems\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/IMAGE/cer4.png\",\n            alt: \"ISO 27001\",\n            className: \"w-10 h-10 object-contain rounded-full border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-foreground\",\n            children: \"ISO 27001 - Information Security\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pt-4 border-t\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-primary rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/IMAGE/message2.png\",\n              alt: \"session\",\n              className: \"w-full h-full object-contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-semibold text-foreground\",\n            children: \"Sessions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => toggleSession(\"targeted\"),\n          className: `w-full text-left p-4 rounded-xl border transition-all shadow mb-4 ${selectedSession === \"targeted\" ? \"border-primary ring-2 ring-primary/20 bg-primary/5\" : \"border-border hover:border-primary/40 hover:bg-accent/50\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-4 h-4 rounded-full border-2 flex items-center justify-center ${selectedSession === \"targeted\" ? \"border-primary\" : \"border-muted\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-2 h-2 rounded-full ${selectedSession === \"targeted\" ? \"bg-primary\" : \"bg-transparent\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-foreground text-base\",\n                children: \"Targeted Session\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              className: \"bg-blue-800 text-white text-xs px-3 py-3\",\n              children: \"150 KWD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 14\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between ml-6 text-sm text-muted-foreground\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"One Specific issue, decision, or review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center \",\n              children: \" 60mins \\u23F1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => toggleSession(\"design\"),\n          className: `w-full text-left p-4 rounded-xl border transition-all shadow mb-6 ${selectedSession === \"design\" ? \"border-primary ring-2 ring-primary/20 bg-primary/5\" : \"border-border hover:border-primary/40 hover:bg-accent/50\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-4 h-4 rounded-full border-2 flex items-center justify-center ${selectedSession === \"design\" ? \"border-primary\" : \"border-muted\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-2 h-2 rounded-full ${selectedSession === \"design\" ? \"bg-primary\" : \"bg-transparent\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-foreground text-base\",\n                children: \"System Design\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              className: \"bg-blue-800 text-white text-xs px-3 py-3\",\n              children: \"250 KWD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 14\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between ml-6 text-sm text-muted-foreground\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Plan or rework a full system or process\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center gap-2\",\n              children: \"120mins \\u23F1 \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => toggleSession(\"monthly\"),\n          className: `w-full text-left p-4 rounded-xl border transition-all shadow ${selectedSession === \"monthly\" ? \"border-primary ring-2 ring-primary/20 bg-primary/5\" : \"border-border hover:border-primary/40 hover:bg-accent/50\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-4 h-4 rounded-full border-2 flex items-center justify-center ${selectedSession === \"monthly\" ? \"border-primary\" : \"border-muted\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-2 h-2 rounded-full ${selectedSession === \"monthly\" ? \"bg-primary\" : \"bg-transparent\"}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-foreground text-base\",\n                children: \"Monthly Advisory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              className: \"bg-slate-200 text-blue-800 text-xs px-3 py-3\",\n              children: \"On Going\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between ml-6 text-sm text-muted-foreground\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Up to 3 sessions / Month + async support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center gap-1\",\n              children: \"Custom \\u23F1 \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonalInfo, \"x4uU1cezqyrD9V7ot3gmoA3xNBg=\");\n_c = PersonalInfo;\nexport default PersonalInfo;\nvar _c;\n$RefreshReg$(_c, \"PersonalInfo\");", "map": {"version": 3, "names": ["useState", "Badge", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "jsxDEV", "_jsxDEV", "PersonalInfo", "_s", "selectedSession", "setSelectedSession", "toggleSession", "sessionId", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/PersonalInfo.js"], "sourcesContent": ["import { useState } from \"react\";\nimport { Badge } from \"../UI/Badge\";\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"../UI/Card\";\n\nconst PersonalInfo = () => {\n  const [selectedSession, setSelectedSession] = useState(null);\n\n  const toggleSession = (sessionId) => {\n    setSelectedSession(sessionId);\n  };\n\n  return (\n    <Card className=\"h-full\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-3\">\n          <div className=\"w-20 h-20 bg-primary rounded-full flex items-center justify-center\">\n            <img\n              src=\"/IMAGE/person.png\"\n              alt=\"Person\"\n              className=\"w-full h-full object-contain\"\n            />\n          </div>\n          <span className=\"text-lg font-semibold\">\n            Personal Information & Certifications\n          </span>\n        </CardTitle>\n      </CardHeader>\n\n      <hr />\n\n      <CardContent className=\"space-y-6\">\n        {/* المعلومات الشخصية */}\n        <div className=\"flex items-center gap-3 p-3 bg-accent rounded-lg\">\n          <div className=\"w-6 h-6 bg-primary rounded-full flex items-center justify-center\">\n            <img\n              src=\"/IMAGE/person2.png\"\n              alt=\"Person2\"\n              className=\"w-full h-full object-contain\"\n            />\n          </div>\n          <div>\n            <div className=\"font-semibold text-foreground\">Alexander Almutairi</div>\n            <div className=\"text-sm text-muted-foreground\">Consultant</div>\n          </div>\n        </div>\n\n        {/* الشهادات */}\n        <div className=\"space-y-2\">\n          <div className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\">\n            <img src=\"/IMAGE/grad.png\" alt=\"MBA\" className=\"w-10 h-10 object-contain rounded-full border\" />\n            <div className=\"font-medium text-foreground\">MBA - Ewha Womans University, South Korea</div>\n          </div>\n\n          <div className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\">\n            <img src=\"/IMAGE/Group.png\" alt=\"Business\" className=\"w-10 h-10 object-contain rounded-full border\" />\n            <div className=\"font-medium text-foreground\">Business & IT Consultant</div>\n          </div>\n\n          <div className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\">\n            <img src=\"/IMAGE/security.png\" alt=\"CEH\" className=\"w-10 h-10 object-contain rounded-full border\" />\n            <div className=\"font-medium text-foreground\">CEH - Certified Ethical Hacker</div>\n          </div>\n\n          <div className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\">\n            <img src=\"/IMAGE/cer4.png\" alt=\"ISO 9001\" className=\"w-10 h-10 object-contain rounded-full border\" />\n            <div className=\"font-medium text-foreground\">ISO 9001 - Quality Management Systems</div>\n          </div>\n\n          <div className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors\">\n            <img src=\"/IMAGE/cer4.png\" alt=\"ISO 27001\" className=\"w-10 h-10 object-contain rounded-full border\" />\n            <div className=\"font-medium text-foreground\">ISO 27001 - Information Security</div>\n          </div>\n        </div>\n\n        {/* الجلسات */}\n        <div className=\"pt-4 border-t\">\n          <div className=\"flex items-center gap-2 mb-4\">\n            <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center\">\n              <img\n                src=\"/IMAGE/message2.png\"\n                alt=\"session\"\n                className=\"w-full h-full object-contain\"\n              />\n            </div>\n            <span className=\"font-semibold text-foreground\">Sessions</span>\n          </div>\n\n          {/* الجلسة 1 */}\n          <button\n            onClick={() => toggleSession(\"targeted\")}\n            className={`w-full text-left p-4 rounded-xl border transition-all shadow mb-4 ${\n              selectedSession === \"targeted\"\n                ? \"border-primary ring-2 ring-primary/20 bg-primary/5\"\n                : \"border-border hover:border-primary/40 hover:bg-accent/50\"\n            }`}\n          >\n            <div className=\"flex justify-between items-center mb-3\">\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${\n                  selectedSession === \"targeted\" ? \"border-primary\" : \"border-muted\"\n                }`}>\n                  <div className={`w-2 h-2 rounded-full ${\n                    selectedSession === \"targeted\" ? \"bg-primary\" : \"bg-transparent\"\n                  }`} />\n                </div>\n                <span className=\"font-semibold text-foreground text-base\">Targeted Session</span>\n              </div>\n             <Badge className=\"bg-blue-800 text-white text-xs px-3 py-3\">150 KWD</Badge>\n            </div>\n            <div className=\"flex justify-between ml-6 text-sm text-muted-foreground\">\n              <p>One Specific issue, decision, or review</p>\n              <span className=\"flex items-center \"> 60mins ⏱</span>\n            </div>\n          </button>\n\n          {/* الجلسة 2 */}\n          <button\n            onClick={() => toggleSession(\"design\")}\n            className={`w-full text-left p-4 rounded-xl border transition-all shadow mb-6 ${\n              selectedSession === \"design\"\n                ? \"border-primary ring-2 ring-primary/20 bg-primary/5\"\n                : \"border-border hover:border-primary/40 hover:bg-accent/50\"\n            }`}\n          >\n            <div className=\"flex justify-between items-center mb-5\">\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${\n                  selectedSession === \"design\" ? \"border-primary\" : \"border-muted\"\n                }`}>\n                  <div className={`w-2 h-2 rounded-full ${\n                    selectedSession === \"design\" ? \"bg-primary\" : \"bg-transparent\"\n                  }`} />\n                </div>\n                <span className=\"font-semibold text-foreground text-base\">System Design</span>\n              </div>\n             <Badge className=\"bg-blue-800 text-white text-xs px-3 py-3\">250 KWD</Badge>\n            </div>\n            <div className=\"flex justify-between ml-6 text-sm text-muted-foreground\">\n              <p>Plan or rework a full system or process</p>\n              <span className=\"flex items-center gap-2\">120mins ⏱ </span>\n            </div>\n          </button>\n\n          {/* الجلسة 3 */}\n          <button\n            onClick={() => toggleSession(\"monthly\")}\n            className={`w-full text-left p-4 rounded-xl border transition-all shadow ${\n              selectedSession === \"monthly\"\n                ? \"border-primary ring-2 ring-primary/20 bg-primary/5\"\n                : \"border-border hover:border-primary/40 hover:bg-accent/50\"\n            }`}\n          >\n            <div className=\"flex justify-between items-center mb-5\">\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${\n                  selectedSession === \"monthly\" ? \"border-primary\" : \"border-muted\"\n                }`}>\n                  <div className={`w-2 h-2 rounded-full ${\n                    selectedSession === \"monthly\" ? \"bg-primary\" : \"bg-transparent\"\n                  }`} />\n                </div>\n                <span className=\"font-semibold text-foreground text-base\">Monthly Advisory</span>\n              </div>\n              <Badge className=\"bg-slate-200 text-blue-800 text-xs px-3 py-3\">\nOn Going</Badge>\n            </div>\n            <div className=\"flex justify-between ml-6 text-sm text-muted-foreground\">\n              <p>Up to 3 sessions / Month + async support</p>\n              <span className=\"flex items-center gap-1\">Custom ⏱ </span>\n            </div>\n          </button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default PersonalInfo;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAMY,aAAa,GAAIC,SAAS,IAAK;IACnCF,kBAAkB,CAACE,SAAS,CAAC;EAC/B,CAAC;EAED,oBACEN,OAAA,CAACL,IAAI;IAACY,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACtBR,OAAA,CAACH,UAAU;MAAAW,QAAA,eACTR,OAAA,CAACF,SAAS;QAACS,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBAC5CR,OAAA;UAAKO,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjFR,OAAA;YACES,GAAG,EAAC,mBAAmB;YACvBC,GAAG,EAAC,QAAQ;YACZH,SAAS,EAAC;UAA8B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNd,OAAA;UAAMO,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAExC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEbd,OAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAENd,OAAA,CAACJ,WAAW;MAACW,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAEhCR,OAAA;QAAKO,SAAS,EAAC,kDAAkD;QAAAC,QAAA,gBAC/DR,OAAA;UAAKO,SAAS,EAAC,kEAAkE;UAAAC,QAAA,eAC/ER,OAAA;YACES,GAAG,EAAC,oBAAoB;YACxBC,GAAG,EAAC,SAAS;YACbH,SAAS,EAAC;UAA8B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNd,OAAA;UAAAQ,QAAA,gBACER,OAAA;YAAKO,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxEd,OAAA;YAAKO,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNd,OAAA;QAAKO,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBR,OAAA;UAAKO,SAAS,EAAC,6EAA6E;UAAAC,QAAA,gBAC1FR,OAAA;YAAKS,GAAG,EAAC,iBAAiB;YAACC,GAAG,EAAC,KAAK;YAACH,SAAS,EAAC;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChGd,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAyC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eAENd,OAAA;UAAKO,SAAS,EAAC,6EAA6E;UAAAC,QAAA,gBAC1FR,OAAA;YAAKS,GAAG,EAAC,kBAAkB;YAACC,GAAG,EAAC,UAAU;YAACH,SAAS,EAAC;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtGd,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAwB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC,eAENd,OAAA;UAAKO,SAAS,EAAC,6EAA6E;UAAAC,QAAA,gBAC1FR,OAAA;YAAKS,GAAG,EAAC,qBAAqB;YAACC,GAAG,EAAC,KAAK;YAACH,SAAS,EAAC;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpGd,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAA8B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eAENd,OAAA;UAAKO,SAAS,EAAC,6EAA6E;UAAAC,QAAA,gBAC1FR,OAAA;YAAKS,GAAG,EAAC,iBAAiB;YAACC,GAAG,EAAC,UAAU;YAACH,SAAS,EAAC;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrGd,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAqC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eAENd,OAAA;UAAKO,SAAS,EAAC,6EAA6E;UAAAC,QAAA,gBAC1FR,OAAA;YAAKS,GAAG,EAAC,iBAAiB;YAACC,GAAG,EAAC,WAAW;YAACH,SAAS,EAAC;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtGd,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EAAC;UAAgC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNd,OAAA;QAAKO,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BR,OAAA;UAAKO,SAAS,EAAC,8BAA8B;UAAAC,QAAA,gBAC3CR,OAAA;YAAKO,SAAS,EAAC,oEAAoE;YAAAC,QAAA,eACjFR,OAAA;cACES,GAAG,EAAC,qBAAqB;cACzBC,GAAG,EAAC,SAAS;cACbH,SAAS,EAAC;YAA8B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNd,OAAA;YAAMO,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAGNd,OAAA;UACEe,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,UAAU,CAAE;UACzCE,SAAS,EAAE,qEACTJ,eAAe,KAAK,UAAU,GAC1B,oDAAoD,GACpD,0DAA0D,EAC7D;UAAAK,QAAA,gBAEHR,OAAA;YAAKO,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDR,OAAA;cAAKO,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCR,OAAA;gBAAKO,SAAS,EAAE,kEACdJ,eAAe,KAAK,UAAU,GAAG,gBAAgB,GAAG,cAAc,EACjE;gBAAAK,QAAA,eACDR,OAAA;kBAAKO,SAAS,EAAE,wBACdJ,eAAe,KAAK,UAAU,GAAG,YAAY,GAAG,gBAAgB;gBAC/D;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNd,OAAA;gBAAMO,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACPd,OAAA,CAACN,KAAK;cAACa,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNd,OAAA;YAAKO,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtER,OAAA;cAAAQ,QAAA,EAAG;YAAuC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9Cd,OAAA;cAAMO,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTd,OAAA;UACEe,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,QAAQ,CAAE;UACvCE,SAAS,EAAE,qEACTJ,eAAe,KAAK,QAAQ,GACxB,oDAAoD,GACpD,0DAA0D,EAC7D;UAAAK,QAAA,gBAEHR,OAAA;YAAKO,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDR,OAAA;cAAKO,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCR,OAAA;gBAAKO,SAAS,EAAE,kEACdJ,eAAe,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cAAc,EAC/D;gBAAAK,QAAA,eACDR,OAAA;kBAAKO,SAAS,EAAE,wBACdJ,eAAe,KAAK,QAAQ,GAAG,YAAY,GAAG,gBAAgB;gBAC7D;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNd,OAAA;gBAAMO,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eACPd,OAAA,CAACN,KAAK;cAACa,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNd,OAAA;YAAKO,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtER,OAAA;cAAAQ,QAAA,EAAG;YAAuC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9Cd,OAAA;cAAMO,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTd,OAAA;UACEe,OAAO,EAAEA,CAAA,KAAMV,aAAa,CAAC,SAAS,CAAE;UACxCE,SAAS,EAAE,gEACTJ,eAAe,KAAK,SAAS,GACzB,oDAAoD,GACpD,0DAA0D,EAC7D;UAAAK,QAAA,gBAEHR,OAAA;YAAKO,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDR,OAAA;cAAKO,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCR,OAAA;gBAAKO,SAAS,EAAE,kEACdJ,eAAe,KAAK,SAAS,GAAG,gBAAgB,GAAG,cAAc,EAChE;gBAAAK,QAAA,eACDR,OAAA;kBAAKO,SAAS,EAAE,wBACdJ,eAAe,KAAK,SAAS,GAAG,YAAY,GAAG,gBAAgB;gBAC9D;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNd,OAAA;gBAAMO,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACNd,OAAA,CAACN,KAAK;cAACa,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YACtE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNd,OAAA;YAAKO,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtER,OAAA;cAAAQ,QAAA,EAAG;YAAwC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC/Cd,OAAA;cAAMO,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACZ,EAAA,CA3KID,YAAY;AAAAe,EAAA,GAAZf,YAAY;AA6KlB,eAAeA,YAAY;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}