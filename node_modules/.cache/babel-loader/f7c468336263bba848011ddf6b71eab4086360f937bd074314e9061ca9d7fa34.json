{"ast": null, "code": "// src/arrow.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"Arrow\";\nvar Arrow = React.forwardRef((props, forwardedRef) => {\n  const {\n    children,\n    width = 10,\n    height = 5,\n    ...arrowProps\n  } = props;\n  return /* @__PURE__ */jsx(Primitive.svg, {\n    ...arrowProps,\n    ref: forwardedRef,\n    width,\n    height,\n    viewBox: \"0 0 30 10\",\n    preserveAspectRatio: \"none\",\n    children: props.asChild ? children : /* @__PURE__ */jsx(\"polygon\", {\n      points: \"0,0 30,0 15,10\"\n    })\n  });\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\nexport { Arrow, Root };", "map": {"version": 3, "names": ["React", "Primitive", "jsx", "NAME", "Arrow", "forwardRef", "props", "forwardedRef", "children", "width", "height", "arrowProps", "svg", "ref", "viewBox", "preserveAspectRatio", "<PERSON><PERSON><PERSON><PERSON>", "points", "displayName", "Root"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/@radix-ui/react-arrow/src/arrow.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ComponentRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AACvB,SAASC,SAAA,QAAiB;AAwBQ,SAAAC,GAAA;AAlBlC,IAAMC,IAAA,GAAO;AAMb,IAAMC,KAAA,GAAcJ,KAAA,CAAAK,UAAA,CAAqC,CAACC,KAAA,EAAOC,YAAA,KAAiB;EAChF,MAAM;IAAEC,QAAA;IAAUC,KAAA,GAAQ;IAAIC,MAAA,GAAS;IAAG,GAAGC;EAAW,IAAIL,KAAA;EAC5D,OACE,eAAAJ,GAAA,CAACD,SAAA,CAAUW,GAAA,EAAV;IACE,GAAGD,UAAA;IACJE,GAAA,EAAKN,YAAA;IACLE,KAAA;IACAC,MAAA;IACAI,OAAA,EAAQ;IACRC,mBAAA,EAAoB;IAGnBP,QAAA,EAAAF,KAAA,CAAMU,OAAA,GAAUR,QAAA,GAAW,eAAAN,GAAA,CAAC;MAAQe,MAAA,EAAO;IAAA,CAAiB;EAAA,CAC/D;AAEJ,CAAC;AAEDb,KAAA,CAAMc,WAAA,GAAcf,IAAA;AAIpB,IAAMgB,IAAA,GAAOf,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}