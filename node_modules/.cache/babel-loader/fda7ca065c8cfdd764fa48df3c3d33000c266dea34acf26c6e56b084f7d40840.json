{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/pages/ConsultationPage.js\";\nimport React from \"react\";\nimport { LanguageProvider } from \"../context/LanguageContext\";\nimport Header from \"../components/UI/Header\";\nimport HeroSection from \"../components/UI/HeroSection\";\nimport ConsultationBooking from \"../components/UI/ConsultationBooking\";\nimport Footer from \"../components/UI/Footer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConsultationPage = () => {\n  return /*#__PURE__*/_jsxDEV(LanguageProvider, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConsultationBooking, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_c = ConsultationPage;\nexport default ConsultationPage;\nvar _c;\n$RefreshReg$(_c, \"ConsultationPage\");", "map": {"version": 3, "names": ["React", "LanguageProvider", "Header", "HeroSection", "ConsultationBooking", "Footer", "jsxDEV", "_jsxDEV", "ConsultationPage", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/pages/ConsultationPage.js"], "sourcesContent": ["import React from \"react\";\nimport { LanguageProvider } from \"../context/LanguageContext\";\nimport Header from \"../components/UI/Header\";\nimport HeroSection from \"../components/UI/HeroSection\";\nimport ConsultationBooking from \"../components/UI/ConsultationBooking\";\nimport Footer from \"../components/UI/Footer\";\n\nconst ConsultationPage = () => {\n  return (\n    <LanguageProvider>\n      <Header />\n      <HeroSection />\n      <ConsultationBooking />\n      <Footer />\n    </LanguageProvider>\n  );\n};\n\nexport default ConsultationPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,MAAM,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA,CAACN,gBAAgB;IAAAQ,QAAA,gBACfF,OAAA,CAACL,MAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVN,OAAA,CAACJ,WAAW;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfN,OAAA,CAACH,mBAAmB;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvBN,OAAA,CAACF,MAAM;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEvB,CAAC;AAACC,EAAA,GATIN,gBAAgB;AAWtB,eAAeA,gBAAgB;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}