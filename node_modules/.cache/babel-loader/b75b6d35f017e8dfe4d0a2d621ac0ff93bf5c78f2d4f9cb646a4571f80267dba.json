{"ast": null, "code": "import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();", "map": {"version": 3, "names": ["createSidecarMedium", "effectCar"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/react-remove-scroll/dist/es2015/medium.js"], "sourcesContent": ["import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,aAAa;AACjD,OAAO,IAAIC,SAAS,GAAGD,mBAAmB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}