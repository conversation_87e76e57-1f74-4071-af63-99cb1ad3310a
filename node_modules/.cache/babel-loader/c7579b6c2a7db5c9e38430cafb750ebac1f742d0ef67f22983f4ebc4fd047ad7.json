{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.3 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.98a2 2 0 0 1 1.69.9l.66 1.2A2 2 0 0 0 12 6h8a2 2 0 0 1 2 2v3.3\",\n  key: \"128dxu\"\n}], [\"path\", {\n  d: \"m14.305 19.53.923-.382\",\n  key: \"3m78fa\"\n}], [\"path\", {\n  d: \"m15.228 16.852-.923-.383\",\n  key: \"npixar\"\n}], [\"path\", {\n  d: \"m16.852 15.228-.383-.923\",\n  key: \"5xggr7\"\n}], [\"path\", {\n  d: \"m16.852 20.772-.383.924\",\n  key: \"dpfhf9\"\n}], [\"path\", {\n  d: \"m19.148 15.228.383-.923\",\n  key: \"1reyyz\"\n}], [\"path\", {\n  d: \"m19.53 21.696-.382-.924\",\n  key: \"1goivc\"\n}], [\"path\", {\n  d: \"m20.772 16.852.924-.383\",\n  key: \"htqkph\"\n}], [\"path\", {\n  d: \"m20.772 19.148.924.383\",\n  key: \"9w9pjp\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}]];\nconst FolderCog = createLucideIcon(\"folder-cog\", __iconNode);\nexport { __iconNode, FolderCog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "FolderCog", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/folder-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10.3 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.98a2 2 0 0 1 1.69.9l.66 1.2A2 2 0 0 0 12 6h8a2 2 0 0 1 2 2v3.3',\n      key: '128dxu',\n    },\n  ],\n  ['path', { d: 'm14.305 19.53.923-.382', key: '3m78fa' }],\n  ['path', { d: 'm15.228 16.852-.923-.383', key: 'npixar' }],\n  ['path', { d: 'm16.852 15.228-.383-.923', key: '5xggr7' }],\n  ['path', { d: 'm16.852 20.772-.383.924', key: 'dpfhf9' }],\n  ['path', { d: 'm19.148 15.228.383-.923', key: '1reyyz' }],\n  ['path', { d: 'm19.53 21.696-.382-.924', key: '1goivc' }],\n  ['path', { d: 'm20.772 16.852.924-.383', key: 'htqkph' }],\n  ['path', { d: 'm20.772 19.148.924.383', key: '9w9pjp' }],\n  ['circle', { cx: '18', cy: '18', r: '3', key: '1xkwt0' }],\n];\n\n/**\n * @component @name FolderCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuMyAyMEg0YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDMuOThhMiAyIDAgMCAxIDEuNjkuOWwuNjYgMS4yQTIgMiAwIDAgMCAxMiA2aDhhMiAyIDAgMCAxIDIgMnYzLjMiIC8+CiAgPHBhdGggZD0ibTE0LjMwNSAxOS41My45MjMtLjM4MiIgLz4KICA8cGF0aCBkPSJtMTUuMjI4IDE2Ljg1Mi0uOTIzLS4zODMiIC8+CiAgPHBhdGggZD0ibTE2Ljg1MiAxNS4yMjgtLjM4My0uOTIzIiAvPgogIDxwYXRoIGQ9Im0xNi44NTIgMjAuNzcyLS4zODMuOTI0IiAvPgogIDxwYXRoIGQ9Im0xOS4xNDggMTUuMjI4LjM4My0uOTIzIiAvPgogIDxwYXRoIGQ9Im0xOS41MyAyMS42OTYtLjM4Mi0uOTI0IiAvPgogIDxwYXRoIGQ9Im0yMC43NzIgMTYuODUyLjkyNC0uMzgzIiAvPgogIDxwYXRoIGQ9Im0yMC43NzIgMTkuMTQ4LjkyNC4zODMiIC8+CiAgPGNpcmNsZSBjeD0iMTgiIGN5PSIxOCIgcj0iMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/folder-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FolderCog = createLucideIcon('folder-cog', __iconNode);\n\nexport default FolderCog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,SAAA,GAAYC,gBAAiB,eAAcP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}