{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.035 17.012a3 3 0 0 0-3-3l-.311-.002a.72.72 0 0 1-.505-1.229l1.195-1.195A2 2 0 0 1 10.828 11H12a2 2 0 0 0 0-4H9.243a3 3 0 0 0-2.122.879l-2.707 2.707A4.83 4.83 0 0 0 3 14a8 8 0 0 0 8 8h2a8 8 0 0 0 8-8V7a2 2 0 1 0-4 0v2a2 2 0 1 0 4 0\",\n  key: \"1ff7rl\"\n}], [\"path\", {\n  d: \"M13.888 9.662A2 2 0 0 0 17 8V5A2 2 0 1 0 13 5\",\n  key: \"1xmd21\"\n}], [\"path\", {\n  d: \"M9 5A2 2 0 1 0 5 5V10\",\n  key: \"f3wfjw\"\n}], [\"path\", {\n  d: \"M9 7V4A2 2 0 1 1 13 4V7.268\",\n  key: \"eaoucv\"\n}]];\nconst HandFist = createLucideIcon(\"hand-fist\", __iconNode);\nexport { __iconNode, HandFist as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "HandFist", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/hand-fist.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.035 17.012a3 3 0 0 0-3-3l-.311-.002a.72.72 0 0 1-.505-1.229l1.195-1.195A2 2 0 0 1 10.828 11H12a2 2 0 0 0 0-4H9.243a3 3 0 0 0-2.122.879l-2.707 2.707A4.83 4.83 0 0 0 3 14a8 8 0 0 0 8 8h2a8 8 0 0 0 8-8V7a2 2 0 1 0-4 0v2a2 2 0 1 0 4 0',\n      key: '1ff7rl',\n    },\n  ],\n  ['path', { d: 'M13.888 9.662A2 2 0 0 0 17 8V5A2 2 0 1 0 13 5', key: '1xmd21' }],\n  ['path', { d: 'M9 5A2 2 0 1 0 5 5V10', key: 'f3wfjw' }],\n  ['path', { d: 'M9 7V4A2 2 0 1 1 13 4V7.268', key: 'eaoucv' }],\n];\n\n/**\n * @component @name HandFist\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuMDM1IDE3LjAxMmEzIDMgMCAwIDAtMy0zbC0uMzExLS4wMDJhLjcyLjcyIDAgMCAxLS41MDUtMS4yMjlsMS4xOTUtMS4xOTVBMiAyIDAgMCAxIDEwLjgyOCAxMUgxMmEyIDIgMCAwIDAgMC00SDkuMjQzYTMgMyAwIDAgMC0yLjEyMi44NzlsLTIuNzA3IDIuNzA3QTQuODMgNC44MyAwIDAgMCAzIDE0YTggOCAwIDAgMCA4IDhoMmE4IDggMCAwIDAgOC04VjdhMiAyIDAgMSAwLTQgMHYyYTIgMiAwIDEgMCA0IDAiIC8+CiAgPHBhdGggZD0iTTEzLjg4OCA5LjY2MkEyIDIgMCAwIDAgMTcgOFY1QTIgMiAwIDEgMCAxMyA1IiAvPgogIDxwYXRoIGQ9Ik05IDVBMiAyIDAgMSAwIDUgNVYxMCIgLz4KICA8cGF0aCBkPSJNOSA3VjRBMiAyIDAgMSAxIDEzIDRWNy4yNjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/hand-fist\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst HandFist = createLucideIcon('hand-fist', __iconNode);\n\nexport default HandFist;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAU,GAC9D;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}