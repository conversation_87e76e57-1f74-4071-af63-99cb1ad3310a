{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/InputField.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Input = ({\n  id,\n  className,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"input\", {\n    id: id,\n    className: `border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`,\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Input;\nvar _c;\n$RefreshReg$(_c, \"Input\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Input", "id", "className", "props", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/InputField.js"], "sourcesContent": ["import React from \"react\";\n\nexport const Input = ({ id, className, ...props }) => {\n  return (\n    <input\n      id={id}\n      className={`border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}\n      {...props}\n    />\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,OAAO,MAAMC,KAAK,GAAGA,CAAC;EAAEC,EAAE;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,KAAK;EACpD,oBACEJ,OAAA;IACEE,EAAE,EAAEA,EAAG;IACPC,SAAS,EAAE,gGAAgGA,SAAS,EAAG;IAAA,GACnHC;EAAK;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC;AAACC,EAAA,GARWR,KAAK;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}