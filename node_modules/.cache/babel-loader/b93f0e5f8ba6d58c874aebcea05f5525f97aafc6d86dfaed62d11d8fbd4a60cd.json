{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/context/LanguageContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useContext, useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LanguageContext = /*#__PURE__*/createContext();\nexport const LanguageProvider = ({\n  children\n}) => {\n  _s();\n  const [language, setLanguage] = useState(\"en\");\n  return /*#__PURE__*/_jsxDEV(LanguageContext.Provider, {\n    value: {\n      language,\n      setLanguage\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_s(LanguageProvider, \"JgNS4s3wc06/6u6z+Ak7Ai5ELN8=\");\n_c = LanguageProvider;\nexport const useLanguage = () => {\n  _s2();\n  return useContext(LanguageContext);\n};\n_s2(useLanguage, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");", "map": {"version": 3, "names": ["createContext", "useContext", "useState", "jsxDEV", "_jsxDEV", "LanguageContext", "LanguageProvider", "children", "_s", "language", "setLanguage", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useLanguage", "_s2", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/context/LanguageContext.js"], "sourcesContent": ["import { createContext, useContext, useState } from \"react\";\n\nconst LanguageContext = createContext();\n\nexport const LanguageProvider = ({ children }) => {\n  const [language, setLanguage] = useState(\"en\");\n\n  return (\n    <LanguageContext.Provider value={{ language, setLanguage }}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\nexport const useLanguage = () => useContext(LanguageContext);\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,eAAe,gBAAGL,aAAa,CAAC,CAAC;AAEvC,OAAO,MAAMM,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAE9C,oBACEE,OAAA,CAACC,eAAe,CAACM,QAAQ;IAACC,KAAK,EAAE;MAAEH,QAAQ;MAAEC;IAAY,CAAE;IAAAH,QAAA,EACxDA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B,CAAC;AAACR,EAAA,CARWF,gBAAgB;AAAAW,EAAA,GAAhBX,gBAAgB;AAU7B,OAAO,MAAMY,WAAW,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAMlB,UAAU,CAACI,eAAe,CAAC;AAAA;AAACc,GAAA,CAAhDD,WAAW;AAAA,IAAAD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}