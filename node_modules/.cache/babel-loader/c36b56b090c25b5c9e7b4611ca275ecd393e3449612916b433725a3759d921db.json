{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 7a2 2 0 0 0-2 2v11\",\n  key: \"1yhqjt\"\n}], [\"path\", {\n  d: \"M5.803 18H5a2 2 0 0 0 0 4h9.5a.5.5 0 0 0 .5-.5V21\",\n  key: \"edzzo5\"\n}], [\"path\", {\n  d: \"M9 15V4a2 2 0 0 1 2-2h9.5a.5.5 0 0 1 .5.5v14a.5.5 0 0 1-.5.5H11a2 2 0 0 1 0-4h10\",\n  key: \"1nwzrg\"\n}]];\nconst BookCopy = createLucideIcon(\"book-copy\", __iconNode);\nexport { __iconNode, BookCopy as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "BookCopy", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/book-copy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 7a2 2 0 0 0-2 2v11', key: '1yhqjt' }],\n  ['path', { d: 'M5.803 18H5a2 2 0 0 0 0 4h9.5a.5.5 0 0 0 .5-.5V21', key: 'edzzo5' }],\n  [\n    'path',\n    {\n      d: 'M9 15V4a2 2 0 0 1 2-2h9.5a.5.5 0 0 1 .5.5v14a.5.5 0 0 1-.5.5H11a2 2 0 0 1 0-4h10',\n      key: '1nwzrg',\n    },\n  ],\n];\n\n/**\n * @component @name BookCopy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSA3YTIgMiAwIDAgMC0yIDJ2MTEiIC8+CiAgPHBhdGggZD0iTTUuODAzIDE4SDVhMiAyIDAgMCAwIDAgNGg5LjVhLjUuNSAwIDAgMCAuNS0uNVYyMSIgLz4KICA8cGF0aCBkPSJNOSAxNVY0YTIgMiAwIDAgMSAyLTJoOS41YS41LjUgMCAwIDEgLjUuNXYxNGEuNS41IDAgMCAxLS41LjVIMTFhMiAyIDAgMCAxIDAtNGgxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/book-copy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookCopy = createLucideIcon('book-copy', __iconNode);\n\nexport default BookCopy;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,mDAAqD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClF,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}