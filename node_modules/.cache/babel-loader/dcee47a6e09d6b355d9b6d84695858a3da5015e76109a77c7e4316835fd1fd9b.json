{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v5.632c0 .424-.272.795-.653.982A6 6 0 0 0 6 14c.006 4 3 7 5 8\",\n  key: \"1h8rid\"\n}], [\"path\", {\n  d: \"M10 5H8a2 2 0 0 0 0 4h.68\",\n  key: \"3ezsi6\"\n}], [\"path\", {\n  d: \"M14 2v5.632c0 .424.272.795.652.982A6 6 0 0 1 18 14c0 4-3 7-5 8\",\n  key: \"yt6q09\"\n}], [\"path\", {\n  d: \"M14 5h2a2 2 0 0 1 0 4h-.68\",\n  key: \"8f95yk\"\n}], [\"path\", {\n  d: \"M18 22H6\",\n  key: \"mg6kv4\"\n}], [\"path\", {\n  d: \"M9 2h6\",\n  key: \"1jrp98\"\n}]];\nconst Amphora = createLucideIcon(\"amphora\", __iconNode);\nexport { __iconNode, Amphora as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Amphora", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/amphora.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    { d: 'M10 2v5.632c0 .424-.272.795-.653.982A6 6 0 0 0 6 14c.006 4 3 7 5 8', key: '1h8rid' },\n  ],\n  ['path', { d: 'M10 5H8a2 2 0 0 0 0 4h.68', key: '3ezsi6' }],\n  ['path', { d: 'M14 2v5.632c0 .424.272.795.652.982A6 6 0 0 1 18 14c0 4-3 7-5 8', key: 'yt6q09' }],\n  ['path', { d: 'M14 5h2a2 2 0 0 1 0 4h-.68', key: '8f95yk' }],\n  ['path', { d: 'M18 22H6', key: 'mg6kv4' }],\n  ['path', { d: 'M9 2h6', key: '1jrp98' }],\n];\n\n/**\n * @component @name Amphora\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnY1LjYzMmMwIC40MjQtLjI3Mi43OTUtLjY1My45ODJBNiA2IDAgMCAwIDYgMTRjLjAwNiA0IDMgNyA1IDgiIC8+CiAgPHBhdGggZD0iTTEwIDVIOGEyIDIgMCAwIDAgMCA0aC42OCIgLz4KICA8cGF0aCBkPSJNMTQgMnY1LjYzMmMwIC40MjQuMjcyLjc5NS42NTIuOTgyQTYgNiAwIDAgMSAxOCAxNGMwIDQtMyA3LTUgOCIgLz4KICA8cGF0aCBkPSJNMTQgNWgyYTIgMiAwIDAgMSAwIDRoLS42OCIgLz4KICA8cGF0aCBkPSJNMTggMjJINiIgLz4KICA8cGF0aCBkPSJNOSAyaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/amphora\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Amphora = createLucideIcon('amphora', __iconNode);\n\nexport default Amphora;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EAAEC,CAAA,EAAG,oEAAsE;EAAAC,GAAA,EAAK;AAAS,EAC3F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,gEAAkE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/F,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAU,GACzC;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}