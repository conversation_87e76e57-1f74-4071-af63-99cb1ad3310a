{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/pages/PaymentPage.js\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport { Button } from \"../components/UI/button\";\nimport { Input } from \"../components/UI/InputField\";\nimport { Label } from \"../components/UI/ Label\";\nimport { Card, CardContent } from \"../components/UI/Card\";\nimport { Checkbox } from \"../components/UI/Checkbox\";\nimport { Lock, CreditCard } from \"lucide-react\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"../components/UI/Select\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaymentForm = () => {\n  _s();\n  const [cardName, setCardName] = useState(\"Card Name Holder\");\n  const [cardNumber, setCardNumber] = useState(\"\");\n  const [cvv, setCvv] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [agreeToUpdates, setAgreeToUpdates] = useState(true);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto p-12 bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-3 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n        className: \"h-6 w-6 text-foreground\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-semibold text-foreground\",\n        children: \"Payment & Confirmation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        className: \"border border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-800 backdrop-blur-lg text-white p-4 rounded-t-lg flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-5 h-5 rounded-full border-2 border-white bg-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-medium\",\n              children: \"Pay with your Bank account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"IMAGE/visa.png\",\n              alt: \"Visa\",\n              className: \"h-15 w-20 object-contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 3\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"IMAGE/visa2.png\",\n              alt: \"Mastercard\",\n              className: \"h-10 w-10 object-contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 3\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 12\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          className: \"p-6 space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(Label, {\n              htmlFor: \"cardType\",\n              className: \"text-sm font-medium\",\n              children: [\"Choose Card Type\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-500\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              defaultValue: \"visa-mastercard\",\n              children: [/*#__PURE__*/_jsxDEV(SelectTrigger, {\n                children: /*#__PURE__*/_jsxDEV(SelectValue, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(SelectContent, {\n                children: [/*#__PURE__*/_jsxDEV(SelectItem, {\n                  value: \"visa-mastercard\",\n                  children: \"Visa / MasterCard\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(SelectItem, {\n                  value: \"american-express\",\n                  children: \"American Express\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid lg:grid-cols-3 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-2 space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"cardName\",\n                  children: [\"Enter Card Name Holder\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-500\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 43\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"cardName\",\n                  value: cardName,\n                  onChange: e => setCardName(e.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(Label, {\n                  htmlFor: \"cardNumber\",\n                  children: [\"Enter Card Number\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-500\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 87,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(Input, {\n                    id: \"cardNumber\",\n                    placeholder: \"Enter 16 Digit Number\",\n                    value: cardNumber,\n                    onChange: e => setCardNumber(e.target.value),\n                    className: \"pr-10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Lock, {\n                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 97,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"bg-payment-gray/50 border-0 p-4 h-32 flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center text-payment-text space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-8 bg-payment-gray rounded mx-auto mb-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium\",\n                    children: \"FULL NAME\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs opacity-75\",\n                    children: \"Card preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"cvv\",\n                children: [\"CVV\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 22\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(Input, {\n                  id: \"cvv\",\n                  value: cvv,\n                  onChange: e => setCvv(e.target.value),\n                  className: \"pr-10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Lock, {\n                  className: \"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(Label, {\n                htmlFor: \"endDate\",\n                children: [\"End Date\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-red-500\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"endDate\",\n                placeholder: \"MM/YY\",\n                value: endDate,\n                onChange: e => setEndDate(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border border-border bg-payment-gray/30 mt-12\",\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          className: \"p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-5 h-5 rounded-full border-2 border-muted-foreground\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-medium\",\n                children: \"Pay With\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-6 justify-center md:justify-end mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"block\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"IMAGE/Mada.png\",\n                  alt: \"Mada\",\n                  className: \"h-10 w-auto object-contain transition-transform duration-300 hover:scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"block\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"IMAGE/tamara.png\",\n                  alt: \"Tamara\",\n                  className: \"h-10 w-auto object-contain transition-transform duration-300 hover:scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#\",\n                className: \"block\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"IMAGE/pay1.png\",\n                  alt: \"Apple Pay\",\n                  className: \"h-10 w-auto object-contain transition-transform duration-300 hover:scale-110\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 3\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 1\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n          id: \"updates\",\n          checked: agreeToUpdates,\n          onCheckedChange: checked => setAgreeToUpdates(checked === true),\n          className: \"mt-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Label, {\n          htmlFor: \"updates\",\n          className: \"text-sm text-payment-text leading-relaxed\",\n          children: \"I do agree to receive updates and promotions about Lucid affiliates or businesses through various channels, including WhatsApp. You can unsubscribe at any time. Read more in our Privacy Policy.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-payment-text\",\n        children: \"A confirmation email with your session details and calendar link will be sent after payment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        className: \"w-full bg-blue-800 hover:bg-blue-900 text-white py-6 text-lg font-medium rounded-lg\",\n        children: \"Confirm & Pay \\uD83D\\uDC46\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(PaymentForm, \"DOhryM+WlZceWouymD/52rOWcgY=\");\n_c = PaymentForm;\nexport default PaymentForm;\nvar _c;\n$RefreshReg$(_c, \"PaymentForm\");", "map": {"version": 3, "names": ["useState", "<PERSON><PERSON>", "Input", "Label", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Checkbox", "Lock", "CreditCard", "Select", "SelectContent", "SelectItem", "SelectTrigger", "SelectValue", "jsxDEV", "_jsxDEV", "PaymentForm", "_s", "cardName", "setCardName", "cardNumber", "setCardNumber", "cvv", "setCvv", "endDate", "setEndDate", "agreeToUpdates", "setAgreeToUpdates", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "htmlFor", "defaultValue", "value", "id", "onChange", "e", "target", "placeholder", "href", "checked", "onCheckedChange", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/pages/PaymentPage.js"], "sourcesContent": ["import { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"../components/UI/button\";\nimport { Input } from \"../components/UI/InputField\";\nimport { Label } from \"../components/UI/ Label\";\nimport { Card, CardContent } from \"../components/UI/Card\";\nimport { Checkbox } from \"../components/UI/Checkbox\";\nimport { Lock, CreditCard } from \"lucide-react\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"../components/UI/Select\";\n\nconst PaymentForm = () => {\n  const [cardName, setCardName] = useState(\"Card Name Holder\");\n  const [cardNumber, setCardNumber] = useState(\"\");\n  const [cvv, setCvv] = useState(\"\");\n  const [endDate, setEndDate] = useState(\"\");\n  const [agreeToUpdates, setAgreeToUpdates] = useState(true);\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-12 bg-background\">\n      {/* Header */}\n      <div className=\"flex items-center gap-3 mb-6\">\n        <CreditCard className=\"h-6 w-6 text-foreground\" />\n        <h1 className=\"text-2xl font-semibold text-foreground\">\n          Payment & Confirmation\n        </h1>\n      </div>\n\n      <div className=\"space-y-6\">\n        {/* Payment Section */}\n        <Card className=\"border border-border\">\n          {/* Header for Bank Account */}\n          <div className=\"bg-blue-800 backdrop-blur-lg text-white p-4 rounded-t-lg flex items-center justify-between\">\n  <div className=\"flex items-center gap-3\">\n    <div className=\"w-5 h-5 rounded-full border-2 border-white bg-white\"></div>\n    <span className=\"text-lg font-medium\">\n      Pay with your Bank account\n    </span>\n  </div>\n\n           <div className=\"flex gap-4\">\n  <img src=\"IMAGE/visa.png\" alt=\"Visa\" className=\"h-15 w-20 object-contain\" />\n  <img src=\"IMAGE/visa2.png\" alt=\"Mastercard\" className=\"h-10 w-10 object-contain\" />\n</div>\n\n          </div>\n\n          <CardContent className=\"p-6 space-y-4\">\n            {/* Card Type */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"cardType\" className=\"text-sm font-medium\">\n                Choose Card Type<span className=\"text-red-500\">*</span>\n              </Label>\n              <Select defaultValue=\"visa-mastercard\">\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"visa-mastercard\">Visa / MasterCard</SelectItem>\n                  <SelectItem value=\"american-express\">American Express</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Card Name Holder + Card Preview */}\n            <div className=\"grid lg:grid-cols-3 gap-6\">\n              <div className=\"lg:col-span-2 space-y-4\">\n                {/* Card Name */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"cardName\">\n                    Enter Card Name Holder<span className=\"text-red-500\">*</span>\n                  </Label>\n                  <Input\n                    id=\"cardName\"\n                    value={cardName}\n                    onChange={(e) => setCardName(e.target.value)}\n                  />\n                </div>\n\n                {/* Card Number */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"cardNumber\">\n                    Enter Card Number<span className=\"text-red-500\">*</span>\n                  </Label>\n                  <div className=\"relative\">\n                    <Input\n                      id=\"cardNumber\"\n                      placeholder=\"Enter 16 Digit Number\"\n                      value={cardNumber}\n                      onChange={(e) => setCardNumber(e.target.value)}\n                      className=\"pr-10\"\n                    />\n                    <Lock className=\"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                  </div>\n                </div>\n              </div>\n\n              {/* Card Preview */}\n              <div className=\"lg:col-span-1\">\n                <Card className=\"bg-payment-gray/50 border-0 p-4 h-32 flex items-center justify-center\">\n                  <div className=\"text-center text-payment-text space-y-2\">\n                    <div className=\"w-12 h-8 bg-payment-gray rounded mx-auto mb-2\"></div>\n                    <div className=\"text-sm font-medium\">FULL NAME</div>\n                    <div className=\"text-xs opacity-75\">Card preview</div>\n                  </div>\n                </Card>\n              </div>\n            </div>\n\n            {/* CVV + End Date */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"cvv\">\n                  CVV<span className=\"text-red-500\">*</span>\n                </Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"cvv\"\n                    value={cvv}\n                     onChange={(e) => setCvv(e.target.value)}\n                    className=\"pr-10\"\n                  />\n                  <Lock className=\"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"endDate\">\n                  End Date<span className=\"text-red-500\">*</span>\n                </Label>\n                <Input\n                  id=\"endDate\"\n                  placeholder=\"MM/YY\"\n                  value={endDate}\n                  onChange={(e) => setEndDate(e.target.value)}\n                />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n       {/* Alternative Payment Methods */}\n<Card className=\"border border-border bg-payment-gray/30 mt-12\">\n  <CardContent className=\"p-6\">\n    <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-6\">\n      {/* العنوان والاختيار */}\n      <div className=\"flex items-center gap-3\">\n        <div className=\"w-5 h-5 rounded-full border-2 border-muted-foreground\"></div>\n        <span className=\"text-lg font-medium\">Pay With</span>\n      </div>\n\n      {/* شعارات طرق الدفع (كمجموعة روابط) */}\n      <div className=\"flex gap-6 justify-center md:justify-end mt-4\">\n        <a href=\"#\" className=\"block\">\n          <img\n            src=\"IMAGE/Mada.png\"\n            alt=\"Mada\"\n            className=\"h-10 w-auto object-contain transition-transform duration-300 hover:scale-110\"\n          />\n        </a>\n        <a href=\"#\" className=\"block\">\n          <img\n            src=\"IMAGE/tamara.png\"\n            alt=\"Tamara\"\n            className=\"h-10 w-auto object-contain transition-transform duration-300 hover:scale-110\"\n          />\n        </a>\n        <a href=\"#\" className=\"block\">\n          <img\n            src=\"IMAGE/pay1.png\"\n            alt=\"Apple Pay\"\n            className=\"h-10 w-auto object-contain transition-transform duration-300 hover:scale-110\"\n          />\n        </a>\n      </div>\n    </div>\n  </CardContent>\n</Card>\n\n        {/* Agreement */}\n        <div className=\"flex items-start space-x-3\">\n          <Checkbox\n            id=\"updates\"\n            checked={agreeToUpdates}\n            onCheckedChange={(checked) => setAgreeToUpdates(checked === true)}\n            className=\"mt-1\"\n          />\n          <Label\n            htmlFor=\"updates\"\n            className=\"text-sm text-payment-text leading-relaxed\"\n          >\n            I do agree to receive updates and promotions about Lucid affiliates\n            or businesses through various channels, including WhatsApp. You can\n            unsubscribe at any time. Read more in our Privacy Policy.\n          </Label>\n        </div>\n\n        {/* Confirmation Note */}\n        <div className=\"text-sm text-payment-text\">\n          A confirmation email with your session details and calendar link will\n          be sent after payment\n        </div>\n\n        {/* Confirm Button */}\n<Button className=\"w-full bg-blue-800 hover:bg-blue-900 text-white py-6 text-lg font-medium rounded-lg\">\n          Confirm & Pay 👆\n        </Button>\n      </div>\n    </div>\n  );\n};\n\nexport default PaymentForm;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,KAAK,QAAQ,6BAA6B;AACnD,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,IAAI,EAAEC,WAAW,QAAQ,uBAAuB;AACzD,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,IAAI,EAAEC,UAAU,QAAQ,cAAc;AAC/C,SACEC,MAAM,EACNC,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,WAAW,QACN,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,kBAAkB,CAAC;EAC5D,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,GAAG,EAAEC,MAAM,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAE1D,oBACEe,OAAA;IAAKa,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAEnDd,OAAA;MAAKa,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3Cd,OAAA,CAACP,UAAU;QAACoB,SAAS,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDlB,OAAA;QAAIa,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENlB,OAAA;MAAKa,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBd,OAAA,CAACX,IAAI;QAACwB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEpCd,OAAA;UAAKa,SAAS,EAAC,4FAA4F;UAAAC,QAAA,gBACnHd,OAAA;YAAKa,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCd,OAAA;cAAKa,SAAS,EAAC;YAAqD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ElB,OAAA;cAAMa,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEGlB,OAAA;YAAKa,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACpCd,OAAA;cAAKmB,GAAG,EAAC,gBAAgB;cAACC,GAAG,EAAC,MAAM;cAACP,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5ElB,OAAA;cAAKmB,GAAG,EAAC,iBAAiB;cAACC,GAAG,EAAC,YAAY;cAACP,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAES,CAAC,eAENlB,OAAA,CAACV,WAAW;UAACuB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEpCd,OAAA;YAAKa,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBd,OAAA,CAACZ,KAAK;cAACiC,OAAO,EAAC,UAAU;cAACR,SAAS,EAAC,qBAAqB;cAAAC,QAAA,GAAC,kBACxC,eAAAd,OAAA;gBAAMa,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACRlB,OAAA,CAACN,MAAM;cAAC4B,YAAY,EAAC,iBAAiB;cAAAR,QAAA,gBACpCd,OAAA,CAACH,aAAa;gBAAAiB,QAAA,eACZd,OAAA,CAACF,WAAW;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAChBlB,OAAA,CAACL,aAAa;gBAAAmB,QAAA,gBACZd,OAAA,CAACJ,UAAU;kBAAC2B,KAAK,EAAC,iBAAiB;kBAAAT,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClElB,OAAA,CAACJ,UAAU;kBAAC2B,KAAK,EAAC,kBAAkB;kBAAAT,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNlB,OAAA;YAAKa,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxCd,OAAA;cAAKa,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBAEtCd,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBd,OAAA,CAACZ,KAAK;kBAACiC,OAAO,EAAC,UAAU;kBAAAP,QAAA,GAAC,wBACF,eAAAd,OAAA;oBAAMa,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACRlB,OAAA,CAACb,KAAK;kBACJqC,EAAE,EAAC,UAAU;kBACbD,KAAK,EAAEpB,QAAS;kBAChBsB,QAAQ,EAAGC,CAAC,IAAKtB,WAAW,CAACsB,CAAC,CAACC,MAAM,CAACJ,KAAK;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNlB,OAAA;gBAAKa,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBd,OAAA,CAACZ,KAAK;kBAACiC,OAAO,EAAC,YAAY;kBAAAP,QAAA,GAAC,mBACT,eAAAd,OAAA;oBAAMa,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACRlB,OAAA;kBAAKa,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBd,OAAA,CAACb,KAAK;oBACJqC,EAAE,EAAC,YAAY;oBACfI,WAAW,EAAC,uBAAuB;oBACnCL,KAAK,EAAElB,UAAW;oBAClBoB,QAAQ,EAAGC,CAAC,IAAKpB,aAAa,CAACoB,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;oBAC/CV,SAAS,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACFlB,OAAA,CAACR,IAAI;oBAACqB,SAAS,EAAC;kBAAmF;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlB,OAAA;cAAKa,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5Bd,OAAA,CAACX,IAAI;gBAACwB,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,eACrFd,OAAA;kBAAKa,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACtDd,OAAA;oBAAKa,SAAS,EAAC;kBAA+C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrElB,OAAA;oBAAKa,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDlB,OAAA;oBAAKa,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlB,OAAA;YAAKa,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCd,OAAA;cAAKa,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBd,OAAA,CAACZ,KAAK;gBAACiC,OAAO,EAAC,KAAK;gBAAAP,QAAA,GAAC,KAChB,eAAAd,OAAA;kBAAMa,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACRlB,OAAA;gBAAKa,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBd,OAAA,CAACb,KAAK;kBACJqC,EAAE,EAAC,KAAK;kBACRD,KAAK,EAAEhB,GAAI;kBACVkB,QAAQ,EAAGC,CAAC,IAAKlB,MAAM,CAACkB,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;kBACzCV,SAAS,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACFlB,OAAA,CAACR,IAAI;kBAACqB,SAAS,EAAC;gBAAmF;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlB,OAAA;cAAKa,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBd,OAAA,CAACZ,KAAK;gBAACiC,OAAO,EAAC,SAAS;gBAAAP,QAAA,GAAC,UACf,eAAAd,OAAA;kBAAMa,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACRlB,OAAA,CAACb,KAAK;gBACJqC,EAAE,EAAC,SAAS;gBACZI,WAAW,EAAC,OAAO;gBACnBL,KAAK,EAAEd,OAAQ;gBACfgB,QAAQ,EAAGC,CAAC,IAAKhB,UAAU,CAACgB,CAAC,CAACC,MAAM,CAACJ,KAAK;cAAE;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGflB,OAAA,CAACX,IAAI;QAACwB,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC7Dd,OAAA,CAACV,WAAW;UAACuB,SAAS,EAAC,KAAK;UAAAC,QAAA,eAC1Bd,OAAA;YAAKa,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBAEjFd,OAAA;cAAKa,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCd,OAAA;gBAAKa,SAAS,EAAC;cAAuD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ElB,OAAA;gBAAMa,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eAGNlB,OAAA;cAAKa,SAAS,EAAC,+CAA+C;cAAAC,QAAA,gBAC5Dd,OAAA;gBAAG6B,IAAI,EAAC,GAAG;gBAAChB,SAAS,EAAC,OAAO;gBAAAC,QAAA,eAC3Bd,OAAA;kBACEmB,GAAG,EAAC,gBAAgB;kBACpBC,GAAG,EAAC,MAAM;kBACVP,SAAS,EAAC;gBAA8E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACJlB,OAAA;gBAAG6B,IAAI,EAAC,GAAG;gBAAChB,SAAS,EAAC,OAAO;gBAAAC,QAAA,eAC3Bd,OAAA;kBACEmB,GAAG,EAAC,kBAAkB;kBACtBC,GAAG,EAAC,QAAQ;kBACZP,SAAS,EAAC;gBAA8E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACJlB,OAAA;gBAAG6B,IAAI,EAAC,GAAG;gBAAChB,SAAS,EAAC,OAAO;gBAAAC,QAAA,eAC3Bd,OAAA;kBACEmB,GAAG,EAAC,gBAAgB;kBACpBC,GAAG,EAAC,WAAW;kBACfP,SAAS,EAAC;gBAA8E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGClB,OAAA;QAAKa,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCd,OAAA,CAACT,QAAQ;UACPiC,EAAE,EAAC,SAAS;UACZM,OAAO,EAAEnB,cAAe;UACxBoB,eAAe,EAAGD,OAAO,IAAKlB,iBAAiB,CAACkB,OAAO,KAAK,IAAI,CAAE;UAClEjB,SAAS,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACFlB,OAAA,CAACZ,KAAK;UACJiC,OAAO,EAAC,SAAS;UACjBR,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACtD;QAID;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlB,OAAA;QAAKa,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EAAC;MAG3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGdlB,OAAA,CAACd,MAAM;QAAC2B,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EAAC;MAEhG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAtMID,WAAW;AAAA+B,EAAA,GAAX/B,WAAW;AAwMjB,eAAeA,WAAW;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}