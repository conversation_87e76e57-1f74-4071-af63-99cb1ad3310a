{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10H6\",\n  key: \"1bsnug\"\n}], [\"path\", {\n  d: \"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2\",\n  key: \"wrbu53\"\n}], [\"path\", {\n  d: \"M19 18h2a1 1 0 0 0 1-1v-3.28a1 1 0 0 0-.684-.948l-1.923-.641a1 1 0 0 1-.578-.502l-1.539-3.076A1 1 0 0 0 16.382 8H14\",\n  key: \"lrkjwd\"\n}], [\"path\", {\n  d: \"M8 8v4\",\n  key: \"1fwk8c\"\n}], [\"path\", {\n  d: \"M9 18h6\",\n  key: \"x1upvd\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"332jqn\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"19iecd\"\n}]];\nconst Ambulance = createLucideIcon(\"ambulance\", __iconNode);\nexport { __iconNode, Ambulance as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Ambulance", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/ambulance.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 10H6', key: '1bsnug' }],\n  ['path', { d: 'M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2', key: 'wrbu53' }],\n  [\n    'path',\n    {\n      d: 'M19 18h2a1 1 0 0 0 1-1v-3.28a1 1 0 0 0-.684-.948l-1.923-.641a1 1 0 0 1-.578-.502l-1.539-3.076A1 1 0 0 0 16.382 8H14',\n      key: 'lrkjwd',\n    },\n  ],\n  ['path', { d: 'M8 8v4', key: '1fwk8c' }],\n  ['path', { d: 'M9 18h6', key: 'x1upvd' }],\n  ['circle', { cx: '17', cy: '18', r: '2', key: '332jqn' }],\n  ['circle', { cx: '7', cy: '18', r: '2', key: '19iecd' }],\n];\n\n/**\n * @component @name Ambulance\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTBINiIgLz4KICA8cGF0aCBkPSJNMTQgMThWNmEyIDIgMCAwIDAtMi0ySDRhMiAyIDAgMCAwLTIgMnYxMWExIDEgMCAwIDAgMSAxaDIiIC8+CiAgPHBhdGgKICAgIGQ9Ik0xOSAxOGgyYTEgMSAwIDAgMCAxLTF2LTMuMjhhMSAxIDAgMCAwLS42ODQtLjk0OGwtMS45MjMtLjY0MWExIDEgMCAwIDEtLjU3OC0uNTAybC0xLjUzOS0zLjA3NkExIDEgMCAwIDAgMTYuMzgyIDhIMTQiIC8+CiAgPHBhdGggZD0iTTggOHY0IiAvPgogIDxwYXRoIGQ9Ik05IDE4aDYiIC8+CiAgPGNpcmNsZSBjeD0iMTciIGN5PSIxOCIgcj0iMiIgLz4KICA8Y2lyY2xlIGN4PSI3IiBjeT0iMTgiIHI9IjIiIC8+Cjwvc3ZnPg==) - https://lucide.dev/icons/ambulance\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ambulance = createLucideIcon('ambulance', __iconNode);\n\nexport default Ambulance;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1F,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,SAAA,GAAYC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}