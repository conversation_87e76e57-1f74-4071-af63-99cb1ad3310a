{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"16\",\n  r: \"1\",\n  key: \"1au0dj\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"12\",\n  x: \"3\",\n  y: \"10\",\n  rx: \"2\",\n  key: \"l0tzu3\"\n}], [\"path\", {\n  d: \"M7 10V7a5 5 0 0 1 9.33-2.5\",\n  key: \"car5b7\"\n}]];\nconst LockKeyholeOpen = createLucideIcon(\"lock-keyhole-open\", __iconNode);\nexport { __iconNode, LockKeyholeOpen as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "width", "height", "x", "y", "rx", "d", "LockKeyholeOpen", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/lock-keyhole-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '16', r: '1', key: '1au0dj' }],\n  ['rect', { width: '18', height: '12', x: '3', y: '10', rx: '2', key: 'l0tzu3' }],\n  ['path', { d: 'M7 10V7a5 5 0 0 1 9.33-2.5', key: 'car5b7' }],\n];\n\n/**\n * @component @name LockKeyholeOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE2IiByPSIxIiAvPgogIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxMiIgeD0iMyIgeT0iMTAiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik03IDEwVjdhNSA1IDAgMCAxIDkuMzMtMi41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lock-keyhole-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LockKeyholeOpen = createLucideIcon('lock-keyhole-open', __iconNode);\n\nexport default LockKeyholeOpen;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC/E,CAAC,MAAQ;EAAEM,CAAA,EAAG,4BAA8B;EAAAN,GAAA,EAAK;AAAU,GAC7D;AAaM,MAAAO,eAAA,GAAkBC,gBAAiB,sBAAqBZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}