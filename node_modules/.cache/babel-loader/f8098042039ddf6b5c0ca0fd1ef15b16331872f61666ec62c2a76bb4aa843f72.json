{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/HeroSection.js\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HeroSection = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"w-full h-[500px] md:h-[600px] bg-cover bg-center\",\n      style: {\n        backgroundImage: \"url('/IMAGE/hero_img.png')\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 5,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mt-6 px-4 sm:px-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-primary text-indigo-800 px-4 py-2 rounded-full text-sm font-medium mb-5 inline-block\",\n        children: \"Book a Consultation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-black mb-4\",\n        children: \"Your Journey Starts Here \\u2013 Book Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm sm:text-base text-neutral-400 max-w-xl mx-auto leading-relaxed line-clamp-2\",\n        children: [\"One-on-one consultations in strategy, IT systems, and digital transformation.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), \"Remote and in-person (Kuwait only) sessions available. Limited monthly availability.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = HeroSection;\nexport default HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");", "map": {"version": 3, "names": ["HeroSection", "_jsxDEV", "_Fragment", "children", "className", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/HeroSection.js"], "sourcesContent": ["const HeroSection = () => {\n  return (\n    <>\n      {/* صورة الخلفية */}\n      <section\n        className=\"w-full h-[500px] md:h-[600px] bg-cover bg-center\"\n        style={{ backgroundImage: \"url('/IMAGE/hero_img.png')\" }}\n      />\n\n      {/* النص بعد الصورة */}\n      <div className=\"text-center mt-6 px-4 sm:px-6\">\n        <div className=\"bg-primary text-indigo-800 px-4 py-2 rounded-full text-sm font-medium mb-5 inline-block\">\n          Book a Consultation\n        </div>\n\n        <h1 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-black mb-4\">\n          Your Journey Starts Here – Book Now\n        </h1>\n\n        <p className=\"text-sm sm:text-base text-neutral-400 max-w-xl mx-auto leading-relaxed line-clamp-2\">\n          One-on-one consultations in strategy, IT systems, and digital transformation.\n          <br />\n          Remote and in-person (Kuwait only) sessions available. Limited monthly availability.\n        </p>\n      </div>\n    </>\n  );\n};\n\n\nexport default HeroSection;\n"], "mappings": ";;AAAA,MAAMA,WAAW,GAAGA,CAAA,KAAM;EACxB,oBACEC,OAAA,CAAAC,SAAA;IAAAC,QAAA,gBAEEF,OAAA;MACEG,SAAS,EAAC,kDAAkD;MAC5DC,KAAK,EAAE;QAAEC,eAAe,EAAE;MAA6B;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,eAGFT,OAAA;MAAKG,SAAS,EAAC,+BAA+B;MAAAD,QAAA,gBAC5CF,OAAA;QAAKG,SAAS,EAAC,yFAAyF;QAAAD,QAAA,EAAC;MAEzG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAENT,OAAA;QAAIG,SAAS,EAAC,4DAA4D;QAAAD,QAAA,EAAC;MAE3E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELT,OAAA;QAAGG,SAAS,EAAC,qFAAqF;QAAAD,QAAA,GAAC,+EAEjG,eAAAF,OAAA;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,wFAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACC,EAAA,GA3BIX,WAAW;AA8BjB,eAAeA,WAAW;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}