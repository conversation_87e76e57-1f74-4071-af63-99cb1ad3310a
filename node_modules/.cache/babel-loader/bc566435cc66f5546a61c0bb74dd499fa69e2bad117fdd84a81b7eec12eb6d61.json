{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/button.js\";\n// src/components/ui/button.js\n\nimport React from \"react\";\nimport clsx from \"clsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Button = ({\n  children,\n  className = \"\",\n  variant = \"default\",\n  // default | outline | ghost\n  type = \"button\",\n  ...props\n}) => {\n  const baseStyles = \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2\";\n  const variants = {\n    default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n    outline: \"border border-input hover:bg-accent hover:text-accent-foreground\",\n    ghost: \"hover:bg-accent hover:text-accent-foreground\"\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    className: clsx(baseStyles, variants[variant], className),\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "clsx", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "className", "variant", "type", "props", "baseStyles", "variants", "default", "outline", "ghost", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/button.js"], "sourcesContent": ["// src/components/ui/button.js\n\nimport React from \"react\";\nimport clsx from \"clsx\";\n\nexport const Button = ({\n  children,\n  className = \"\",\n  variant = \"default\", // default | outline | ghost\n  type = \"button\",\n  ...props\n}) => {\n  const baseStyles = \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2\";\n\n  const variants = {\n    default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n    outline: \"border border-input hover:bg-accent hover:text-accent-foreground\",\n    ghost: \"hover:bg-accent hover:text-accent-foreground\",\n  };\n\n  return (\n    <button\n      type={type}\n      className={clsx(baseStyles, variants[variant], className)}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n"], "mappings": ";AAAA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,OAAO,MAAMC,MAAM,GAAGA,CAAC;EACrBC,QAAQ;EACRC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,SAAS;EAAE;EACrBC,IAAI,GAAG,QAAQ;EACf,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,UAAU,GAAG,yNAAyN;EAE5O,MAAMC,QAAQ,GAAG;IACfC,OAAO,EAAE,wDAAwD;IACjEC,OAAO,EAAE,kEAAkE;IAC3EC,KAAK,EAAE;EACT,CAAC;EAED,oBACEX,OAAA;IACEK,IAAI,EAAEA,IAAK;IACXF,SAAS,EAAEL,IAAI,CAACS,UAAU,EAAEC,QAAQ,CAACJ,OAAO,CAAC,EAAED,SAAS,CAAE;IAAA,GACtDG,KAAK;IAAAJ,QAAA,EAERA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACC,EAAA,GAxBWf,MAAM;AAAA,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}