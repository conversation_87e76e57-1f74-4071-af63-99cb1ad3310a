{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/Footer.js\";\nimport { But<PERSON> } from \"../UI/button\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-primary text-primary-foreground mt-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-1 py-18\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 text-blue-900\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-white p-1\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/IMAGE/logo_header.png\",\n                alt: \"Logo\",\n                className: \"w-full h-full object-contain\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 13,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold text-base sm:text-lg\",\n                children: \"LUCID INNOVATION\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs opacity-80\",\n                children: \"\\u0644\\u0648\\u0633\\u064A\\u062F \\u0627\\u0646\\u0648\\u06A4\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u0640\\u064A\\u0634\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white p-1\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/IMAGE/Phone.png\",\n                  alt: \"Phone\",\n                  className: \"w-full h-full object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 34,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Telephone: +965 2297 3847\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Fax: +965 2297 3888\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 42,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white p-1\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/IMAGE/Mail.png\",\n                  alt: \"Mail\",\n                  className: \"w-full h-full object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Email: <EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white p-1\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/IMAGE/Address.png\",\n                  alt: \"Address\",\n                  className: \"w-full h-full object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Address: Sahab Tower, level 19\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Safha 4, Kuwait - City\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Kuwait\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-10 h-10 bg-white p-1\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/IMAGE/message.png\",\n                  alt: \"PO Box\",\n                  className: \"w-full h-full object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"P.O. Box: E26 Dasman 15443\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"Kuwait City Kuwait\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-blue-900 mt-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold mb-4 \",\n              children: \"Quick Links\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-5 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"hover:underline\",\n                  children: \"Features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"hover:underline\",\n                  children: \"Case Studies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"hover:underline\",\n                  children: \"Reviews\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  className: \"hover:underline\",\n                  children: \"Updates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 22\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:col-span-2 mt-12 text-blue-900\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold mb-4\",\n            children: \"Subscribe to our newsletter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm opacity-80 mb-4\",\n            children: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor et labore. Lorem ipsum dolor sit amet.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Enter your email\",\n              className: \"w-full py-2 px-4 pr-28 rounded-full bg-blue-900 text-white placeholder-white border border-blue-900 focus:outline-none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"absolute right-1 top-1 bottom-1 px-4 rounded-full bg-white text-blue-900 text-sm border border-blue-900\",\n              children: \"Subscribe\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-primary-foreground/20 mt-8 pt-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm font-medium text-blue-900 mb-2\",\n              children: \"We're Certified By\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-24 h-24 bg-primary-foreground rounded-full flex items-center justify-center shadow-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-20 bg-white p-2 rounded-full\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/IMAGE/cer1.png\",\n                    alt: \"Cer1\",\n                    className: \"w-full h-full object-contain\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-24 h-24 bg-primary-foreground rounded-full flex items-center justify-center shadow-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-20 bg-white p-2 rounded-full\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/IMAGE/cer2.png\",\n                    alt: \"Cer2\",\n                    className: \"w-full h-full object-contain\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-24 h-24 bg-primary-foreground rounded-full flex items-center justify-center shadow-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-20 h-20 bg-white p-2 rounded-full\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/IMAGE/cer3.png\",\n                    alt: \"Cer3\",\n                    className: \"w-full h-full object-contain\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"bg-black text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:opacity-90\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/IMAGE/apple.png\",\n                alt: \"Apple\",\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left text-xs font-semibold\",\n                children: \"App Store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"bg-black text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:opacity-90\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/IMAGE/google.png\",\n                alt: \"Google\",\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-left text-xs font-semibold\",\n                children: \"Google Play\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-blue-900 text-white mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-4 py-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center text-sm opacity-80\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Copyright \\xA9 2023 Lucid | All Rights Reserved\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-6 mt-2 md:mt-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"hover:underline\",\n              children: \"About\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"hover:underline\",\n              children: \"Contact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"hover:underline\",\n              children: \"Help Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"hover:underline\",\n              children: \"Chat Support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/Footer.js"], "sourcesContent": ["import { Button } from \"../UI/button\";\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-primary text-primary-foreground mt-12\">\n      <div className=\"container mx-auto px-1 py-18\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4 text-blue-900\">\n            {/* Logo and Name */}\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-10 h-10 bg-white p-1\">\n                <img\n                  src=\"/IMAGE/logo_header.png\"\n                  alt=\"Logo\"\n                  className=\"w-full h-full object-contain\"\n                />\n              </div>\n              <div>\n                <div className=\"font-bold text-base sm:text-lg\">\n                  LUCID INNOVATION\n                </div>\n                <div className=\"text-xs opacity-80\">\n                  لوسيد انوڤــــــــــــــــــيشن\n                </div>\n              </div>\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-3 text-sm\">\n              {/* Phone */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-white p-1\">\n                  <img\n                    src=\"/IMAGE/Phone.png\"\n                    alt=\"Phone\"\n                    className=\"w-full h-full object-contain\"\n                  />\n                </div>\n                <div>\n                  <div>Telephone: +965 2297 3847</div>\n                  <div>Fax: +965 2297 3888</div>\n                </div>\n              </div>\n\n              {/* Email */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-white p-1\">\n                  <img\n                    src=\"/IMAGE/Mail.png\"\n                    alt=\"Mail\"\n                    className=\"w-full h-full object-contain\"\n                  />\n                </div>\n                <div>\n                  <div>Email: <EMAIL></div>\n                  <div><EMAIL></div>\n                </div>\n              </div>\n\n              {/* Address */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-white p-1\">\n                  <img\n                    src=\"/IMAGE/Address.png\"\n                    alt=\"Address\"\n                    className=\"w-full h-full object-contain\"\n                  />\n                </div>\n                <div>\n                  <div>Address: Sahab Tower, level 19</div>\n                  <div>Safha 4, Kuwait - City</div>\n                  <div>Kuwait</div>\n                </div>\n              </div>\n\n              {/* P.O. Box */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-white p-1\">\n                  <img\n                    src=\"/IMAGE/message.png\"\n                    alt=\"PO Box\"\n                    className=\"w-full h-full object-contain\"\n                  />\n                </div>\n                <div>\n                  <div>P.O. Box: E26 Dasman 15443</div>\n                  <div>Kuwait City Kuwait</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <div className=\"text-blue-900 mt-12\">\n              <h3 className=\"font-semibold mb-4 \">Quick Links</h3>\n              <div className=\"space-y-5 text-sm\">\n                <div><a href=\"#\" className=\"hover:underline\">Features</a></div>\n                <div><a href=\"#\" className=\"hover:underline\">Case Studies</a></div>\n                <div><a href=\"#\" className=\"hover:underline\">Reviews</a></div>\n                <div><a href=\"#\" className=\"hover:underline\">Updates</a></div>\n              </div>\n            </div>\n          </div>\n\n          {/* Newsletter */}\n          <div className=\"md:col-span-2 mt-12 text-blue-900\">\n            <h3 className=\"font-semibold mb-4\">Subscribe to our newsletter</h3>\n            <p className=\"text-sm opacity-80 mb-4\">\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor et labore. Lorem ipsum dolor sit amet.\n            </p>\n            <div className=\"relative w-full max-w-md mx-auto\">\n              <input\n                type=\"text\"\n                placeholder=\"Enter your email\"\n                className=\"w-full py-2 px-4 pr-28 rounded-full bg-blue-900 text-white placeholder-white border border-blue-900 focus:outline-none\"\n              />\n              <button\n                className=\"absolute right-1 top-1 bottom-1 px-4 rounded-full bg-white text-blue-900 text-sm border border-blue-900\"\n              >\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-primary-foreground/20 mt-8 pt-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0\">\n            {/* Certifications */}\n            <div>\n              <div className=\"text-sm font-medium text-blue-900 mb-2\">We're Certified By</div>\n              <div className=\"flex items-center gap-8\">\n                {/* Certificate 1 */}\n                <div className=\"w-24 h-24 bg-primary-foreground rounded-full flex items-center justify-center shadow-md\">\n                  <div className=\"w-20 h-20 bg-white p-2 rounded-full\">\n                    <img\n                      src=\"/IMAGE/cer1.png\"\n                      alt=\"Cer1\"\n                      className=\"w-full h-full object-contain\"\n                    />\n                  </div>\n                </div>\n\n                {/* Certificate 2 */}\n                <div className=\"w-24 h-24 bg-primary-foreground rounded-full flex items-center justify-center shadow-md\">\n                  <div className=\"w-20 h-20 bg-white p-2 rounded-full\">\n                    <img\n                      src=\"/IMAGE/cer2.png\"\n                      alt=\"Cer2\"\n                      className=\"w-full h-full object-contain\"\n                    />\n                  </div>\n                </div>\n\n                {/* Certificate 3 - نفس الحجم */}\n                <div className=\"w-24 h-24 bg-primary-foreground rounded-full flex items-center justify-center shadow-md\">\n                  <div className=\"w-20 h-20 bg-white p-2 rounded-full\">\n                    <img\n                      src=\"/IMAGE/cer3.png\"\n                      alt=\"Cer3\"\n                      className=\"w-full h-full object-contain\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* App Store Buttons */}\n            <div className=\"flex space-x-3\">\n              {/* App Store */}\n              <button className=\"bg-black text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:opacity-90\">\n                <img src=\"/IMAGE/apple.png\" alt=\"Apple\" className=\"w-5 h-5\" />\n                <div className=\"text-left text-xs font-semibold\">\n                  App Store\n                </div>\n              </button>\n\n              {/* Google Play */}\n              <button className=\"bg-black text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:opacity-90\">\n                <img src=\"/IMAGE/google.png\" alt=\"Google\" className=\"w-5 h-5\" />\n                <div className=\"text-left text-xs font-semibold\">\n                  Google Play\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Full-width Footer Links Bar */}\n      <div className=\"bg-blue-900 text-white mt-6\">\n        <div className=\"container mx-auto px-4 py-3\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center text-sm opacity-80\">\n            <div>Copyright © 2023 Lucid | All Rights Reserved</div>\n            <div className=\"flex space-x-6 mt-2 md:mt-0\">\n              <a href=\"#\" className=\"hover:underline\">About</a>\n              <a href=\"#\" className=\"hover:underline\">Contact</a>\n              <a href=\"#\" className=\"hover:underline\">Help Center</a>\n              <a href=\"#\" className=\"hover:underline\">Chat Support</a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,0CAA0C;IAAAC,QAAA,gBAC1DH,OAAA;MAAKE,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CH,OAAA;QAAKE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDH,OAAA;UAAKE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtCH,OAAA;YAAKE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CH,OAAA;cAAKE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,eACrCH,OAAA;gBACEI,GAAG,EAAC,wBAAwB;gBAC5BC,GAAG,EAAC,MAAM;gBACVH,SAAS,EAAC;cAA8B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNT,OAAA;cAAAG,QAAA,gBACEH,OAAA;gBAAKE,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAEhD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNT,OAAA;gBAAKE,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAEpC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNT,OAAA;YAAKE,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAEhCH,OAAA;cAAKE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CH,OAAA;gBAAKE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCH,OAAA;kBACEI,GAAG,EAAC,kBAAkB;kBACtBC,GAAG,EAAC,OAAO;kBACXH,SAAS,EAAC;gBAA8B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNT,OAAA;gBAAAG,QAAA,gBACEH,OAAA;kBAAAG,QAAA,EAAK;gBAAyB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpCT,OAAA;kBAAAG,QAAA,EAAK;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNT,OAAA;cAAKE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CH,OAAA;gBAAKE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCH,OAAA;kBACEI,GAAG,EAAC,iBAAiB;kBACrBC,GAAG,EAAC,MAAM;kBACVH,SAAS,EAAC;gBAA8B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNT,OAAA;gBAAAG,QAAA,gBACEH,OAAA;kBAAAG,QAAA,EAAK;gBAAwB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnCT,OAAA;kBAAAG,QAAA,EAAK;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNT,OAAA;cAAKE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CH,OAAA;gBAAKE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCH,OAAA;kBACEI,GAAG,EAAC,oBAAoB;kBACxBC,GAAG,EAAC,SAAS;kBACbH,SAAS,EAAC;gBAA8B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNT,OAAA;gBAAAG,QAAA,gBACEH,OAAA;kBAAAG,QAAA,EAAK;gBAA8B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACzCT,OAAA;kBAAAG,QAAA,EAAK;gBAAsB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjCT,OAAA;kBAAAG,QAAA,EAAK;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNT,OAAA;cAAKE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CH,OAAA;gBAAKE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,eACrCH,OAAA;kBACEI,GAAG,EAAC,oBAAoB;kBACxBC,GAAG,EAAC,QAAQ;kBACZH,SAAS,EAAC;gBAA8B;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNT,OAAA;gBAAAG,QAAA,gBACEH,OAAA;kBAAAG,QAAA,EAAK;gBAA0B;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCT,OAAA;kBAAAG,QAAA,EAAK;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAAG,QAAA,eACEH,OAAA;YAAKE,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCH,OAAA;cAAIE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpDT,OAAA;cAAKE,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCH,OAAA;gBAAAG,QAAA,eAAKH,OAAA;kBAAGU,IAAI,EAAC,GAAG;kBAACR,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DT,OAAA;gBAAAG,QAAA,eAAKH,OAAA;kBAAGU,IAAI,EAAC,GAAG;kBAACR,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnET,OAAA;gBAAAG,QAAA,eAAKH,OAAA;kBAAGU,IAAI,EAAC,GAAG;kBAACR,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9DT,OAAA;gBAAAG,QAAA,eAAKH,OAAA;kBAAGU,IAAI,EAAC,GAAG;kBAACR,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAKE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDH,OAAA;YAAIE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA2B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnET,OAAA;YAAGE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJT,OAAA;YAAKE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CH,OAAA;cACEW,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kBAAkB;cAC9BV,SAAS,EAAC;YAAwH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnI,CAAC,eACFT,OAAA;cACEE,SAAS,EAAC,yGAAyG;cAAAC,QAAA,EACpH;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNT,OAAA;QAAKE,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eAC9DH,OAAA;UAAKE,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAE5FH,OAAA;YAAAG,QAAA,gBACEH,OAAA;cAAKE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChFT,OAAA;cAAKE,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBAEtCH,OAAA;gBAAKE,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,eACtGH,OAAA;kBAAKE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDH,OAAA;oBACEI,GAAG,EAAC,iBAAiB;oBACrBC,GAAG,EAAC,MAAM;oBACVH,SAAS,EAAC;kBAA8B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNT,OAAA;gBAAKE,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,eACtGH,OAAA;kBAAKE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDH,OAAA;oBACEI,GAAG,EAAC,iBAAiB;oBACrBC,GAAG,EAAC,MAAM;oBACVH,SAAS,EAAC;kBAA8B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNT,OAAA;gBAAKE,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,eACtGH,OAAA;kBAAKE,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAClDH,OAAA;oBACEI,GAAG,EAAC,iBAAiB;oBACrBC,GAAG,EAAC,MAAM;oBACVH,SAAS,EAAC;kBAA8B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNT,OAAA;YAAKE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAE7BH,OAAA;cAAQE,SAAS,EAAC,uFAAuF;cAAAC,QAAA,gBACvGH,OAAA;gBAAKI,GAAG,EAAC,kBAAkB;gBAACC,GAAG,EAAC,OAAO;gBAACH,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DT,OAAA;gBAAKE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGTT,OAAA;cAAQE,SAAS,EAAC,uFAAuF;cAAAC,QAAA,gBACvGH,OAAA;gBAAKI,GAAG,EAAC,mBAAmB;gBAACC,GAAG,EAAC,QAAQ;gBAACH,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChET,OAAA;gBAAKE,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNT,OAAA;MAAKE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CH,OAAA;QAAKE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CH,OAAA;UAAKE,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxFH,OAAA;YAAAG,QAAA,EAAK;UAA4C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvDT,OAAA;YAAKE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CH,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjDT,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACnDT,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAW;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvDT,OAAA;cAAGU,IAAI,EAAC,GAAG;cAACR,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACI,EAAA,GA7MIZ,MAAM;AA+MZ,eAAeA,MAAM;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}