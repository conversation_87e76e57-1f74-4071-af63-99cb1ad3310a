{"ast": null, "code": "/**\n * creates a Ref object with on change callback\n * @param callback\n * @returns {RefObject}\n *\n * @see {@link useCallbackRef}\n * @see https://reactjs.org/docs/refs-and-the-dom.html#creating-refs\n */\nexport function createCallbackRef(callback) {\n  var current = null;\n  return {\n    get current() {\n      return current;\n    },\n    set current(value) {\n      var last = current;\n      if (last !== value) {\n        current = value;\n        callback(value, last);\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["createCallbackRef", "callback", "current", "value", "last"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/use-callback-ref/dist/es2015/createRef.js"], "sourcesContent": ["/**\n * creates a Ref object with on change callback\n * @param callback\n * @returns {RefObject}\n *\n * @see {@link useCallbackRef}\n * @see https://reactjs.org/docs/refs-and-the-dom.html#creating-refs\n */\nexport function createCallbackRef(callback) {\n    var current = null;\n    return {\n        get current() {\n            return current;\n        },\n        set current(value) {\n            var last = current;\n            if (last !== value) {\n                current = value;\n                callback(value, last);\n            }\n        },\n    };\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,iBAAiBA,CAACC,QAAQ,EAAE;EACxC,IAAIC,OAAO,GAAG,IAAI;EAClB,OAAO;IACH,IAAIA,OAAOA,CAAA,EAAG;MACV,OAAOA,OAAO;IAClB,CAAC;IACD,IAAIA,OAAOA,CAACC,KAAK,EAAE;MACf,IAAIC,IAAI,GAAGF,OAAO;MAClB,IAAIE,IAAI,KAAKD,KAAK,EAAE;QAChBD,OAAO,GAAGC,KAAK;QACfF,QAAQ,CAACE,KAAK,EAAEC,IAAI,CAAC;MACzB;IACJ;EACJ,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}