{"ast": null, "code": "// src/visually-hidden.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = React.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */jsx(Primitive.span, {\n    ...props,\n    ref: forwardedRef,\n    style: {\n      ...VISUALLY_HIDDEN_STYLES,\n      ...props.style\n    }\n  });\n});\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\nexport { Root, VISUALLY_HIDDEN_STYLES, VisuallyHidden };", "map": {"version": 3, "names": ["React", "Primitive", "jsx", "VISUALLY_HIDDEN_STYLES", "Object", "freeze", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap", "NAME", "VisuallyHidden", "forwardRef", "props", "forwardedRef", "span", "ref", "style", "displayName", "Root"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/@radix-ui/react-visually-hidden/src/visually-hidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: 'absolute',\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: 'hidden',\n  clip: 'rect(0, 0, 0, 0)',\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}) satisfies React.CSSProperties;\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n  //\n  VISUALLY_HIDDEN_STYLES,\n};\nexport type { VisuallyHiddenProps };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AACvB,SAASC,SAAA,QAAiB;AA6BpB,SAAAC,GAAA;AAvBN,IAAMC,sBAAA,GAAyBC,MAAA,CAAOC,MAAA,CAAO;EAAA;EAE3CC,QAAA,EAAU;EACVC,MAAA,EAAQ;EACRC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,OAAA,EAAS;EACTC,MAAA,EAAQ;EACRC,QAAA,EAAU;EACVC,IAAA,EAAM;EACNC,UAAA,EAAY;EACZC,QAAA,EAAU;AACZ,CAAC;AAED,IAAMC,IAAA,GAAO;AAMb,IAAMC,cAAA,GAAuBjB,KAAA,CAAAkB,UAAA,CAC3B,CAACC,KAAA,EAAOC,YAAA,KAAiB;EACvB,OACE,eAAAlB,GAAA,CAACD,SAAA,CAAUoB,IAAA,EAAV;IACE,GAAGF,KAAA;IACJG,GAAA,EAAKF,YAAA;IACLG,KAAA,EAAO;MAAE,GAAGpB,sBAAA;MAAwB,GAAGgB,KAAA,CAAMI;IAAM;EAAA,CACrD;AAEJ,CACF;AAEAN,cAAA,CAAeO,WAAA,GAAcR,IAAA;AAI7B,IAAMS,IAAA,GAAOR,cAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}