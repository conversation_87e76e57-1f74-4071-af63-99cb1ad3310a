{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"11.5\",\n  cy: \"12.5\",\n  r: \"3.5\",\n  key: \"1cl1mi\"\n}], [\"path\", {\n  d: \"M3 8c0-3.5 2.5-6 6.5-6 5 0 4.83 3 7.5 5s5 2 5 6c0 4.5-2.5 6.5-7 6.5-2.5 0-2.5 2.5-6 2.5s-7-2-7-5.5c0-3 1.5-3 1.5-5C3.5 10 3 9 3 8Z\",\n  key: \"165ef9\"\n}]];\nconst EggFried = createLucideIcon(\"egg-fried\", __iconNode);\nexport { __iconNode, EggFried as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "EggFried", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/egg-fried.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '11.5', cy: '12.5', r: '3.5', key: '1cl1mi' }],\n  [\n    'path',\n    {\n      d: 'M3 8c0-3.5 2.5-6 6.5-6 5 0 4.83 3 7.5 5s5 2 5 6c0 4.5-2.5 6.5-7 6.5-2.5 0-2.5 2.5-6 2.5s-7-2-7-5.5c0-3 1.5-3 1.5-5C3.5 10 3 9 3 8Z',\n      key: '165ef9',\n    },\n  ],\n];\n\n/**\n * @component @name EggFried\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMS41IiBjeT0iMTIuNSIgcj0iMy41IiAvPgogIDxwYXRoIGQ9Ik0zIDhjMC0zLjUgMi41LTYgNi41LTYgNSAwIDQuODMgMyA3LjUgNXM1IDIgNSA2YzAgNC41LTIuNSA2LjUtNyA2LjUtMi41IDAtMi41IDIuNS02IDIuNXMtNy0yLTctNS41YzAtMyAxLjUtMyAxLjUtNUMzLjUgMTAgMyA5IDMgOFoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/egg-fried\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EggFried = createLucideIcon('egg-fried', __iconNode);\n\nexport default EggFried;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAOC,GAAK;AAAA,CAAU,GAC9D,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAE,QAAA,GAAWC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}