{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/ Label.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Label = ({\n  htmlFor,\n  className,\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"label\", {\n    htmlFor: htmlFor,\n    className: `text-sm font-medium text-gray-700 ${className}`,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Label;\nvar _c;\n$RefreshReg$(_c, \"Label\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Label", "htmlFor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/ Label.js"], "sourcesContent": ["import React from \"react\";\n\nexport const Label = ({ htmlFor, className, children }) => {\n  return (\n    <label\n      htmlFor={htmlFor}\n      className={`text-sm font-medium text-gray-700 ${className}`}\n    >\n      {children}\n    </label>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,OAAO,MAAMC,KAAK,GAAGA,CAAC;EAAEC,OAAO;EAAEC,SAAS;EAAEC;AAAS,CAAC,KAAK;EACzD,oBACEJ,OAAA;IACEE,OAAO,EAAEA,OAAQ;IACjBC,SAAS,EAAE,qCAAqCA,SAAS,EAAG;IAAAC,QAAA,EAE3DA;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEZ,CAAC;AAACC,EAAA,GATWR,KAAK;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}