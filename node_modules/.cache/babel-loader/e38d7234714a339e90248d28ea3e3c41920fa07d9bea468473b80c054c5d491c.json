{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 14h2a2 2 0 0 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 16\",\n  key: \"1v1a37\"\n}], [\"path\", {\n  d: \"m14.45 13.39 5.05-4.694C20.196 8 21 6.85 21 5.75a2.75 2.75 0 0 0-4.797-1.837.276.276 0 0 1-.406 0A2.75 2.75 0 0 0 11 5.75c0 1.2.802 2.248 1.5 2.946L16 11.95\",\n  key: \"fhfbnt\"\n}], [\"path\", {\n  d: \"m2 15 6 6\",\n  key: \"10dquu\"\n}], [\"path\", {\n  d: \"m7 20 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a1 1 0 0 0-2.75-2.91\",\n  key: \"1x6kdw\"\n}]];\nconst HandHeart = createLucideIcon(\"hand-heart\", __iconNode);\nexport { __iconNode, HandHeart as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "HandHeart", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/hand-heart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M11 14h2a2 2 0 0 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 16', key: '1v1a37' }],\n  [\n    'path',\n    {\n      d: 'm14.45 13.39 5.05-4.694C20.196 8 21 6.85 21 5.75a2.75 2.75 0 0 0-4.797-1.837.276.276 0 0 1-.406 0A2.75 2.75 0 0 0 11 5.75c0 1.2.802 2.248 1.5 2.946L16 11.95',\n      key: 'fhfbnt',\n    },\n  ],\n  ['path', { d: 'm2 15 6 6', key: '10dquu' }],\n  [\n    'path',\n    {\n      d: 'm7 20 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a1 1 0 0 0-2.75-2.91',\n      key: '1x6kdw',\n    },\n  ],\n];\n\n/**\n * @component @name HandHeart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMTRoMmEyIDIgMCAwIDAgMC00aC0zYy0uNiAwLTEuMS4yLTEuNC42TDMgMTYiIC8+CiAgPHBhdGggZD0ibTE0LjQ1IDEzLjM5IDUuMDUtNC42OTRDMjAuMTk2IDggMjEgNi44NSAyMSA1Ljc1YTIuNzUgMi43NSAwIDAgMC00Ljc5Ny0xLjgzNy4yNzYuMjc2IDAgMCAxLS40MDYgMEEyLjc1IDIuNzUgMCAwIDAgMTEgNS43NWMwIDEuMi44MDIgMi4yNDggMS41IDIuOTQ2TDE2IDExLjk1IiAvPgogIDxwYXRoIGQ9Im0yIDE1IDYgNiIgLz4KICA8cGF0aCBkPSJtNyAyMCAxLjYtMS40Yy4zLS40LjgtLjYgMS40LS42aDRjMS4xIDAgMi4xLS40IDIuOC0xLjJsNC42LTQuNGExIDEgMCAwIDAtMi43NS0yLjkxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/hand-heart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst HandHeart = createLucideIcon('hand-heart', __iconNode);\n\nexport default HandHeart;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kDAAoD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjF,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,eAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}