{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/Select.js\";\nimport * as React from \"react\";\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\nimport { Check, ChevronDown } from \"lucide-react\";\nimport { cn } from \"../../lib/utils\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Select = SelectPrimitive.Root;\nconst SelectTrigger = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  children,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(SelectPrimitive.Trigger, {\n  ref: ref,\n  className: cn(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n  ...props,\n  children: [children, /*#__PURE__*/_jsxDEV(SelectPrimitive.Icon, {\n    asChild: true,\n    children: /*#__PURE__*/_jsxDEV(ChevronDown, {\n      className: \"h-4 w-4 opacity-50\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 10,\n  columnNumber: 3\n}, this));\n_c2 = SelectTrigger;\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\nconst SelectValue = SelectPrimitive.Value;\nconst SelectContent = /*#__PURE__*/React.forwardRef(_c3 = ({\n  className,\n  children,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(SelectPrimitive.Portal, {\n  children: /*#__PURE__*/_jsxDEV(SelectPrimitive.Content, {\n    ref: ref,\n    className: cn(\"relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-80\", className),\n    ...props,\n    children: /*#__PURE__*/_jsxDEV(SelectPrimitive.Viewport, {\n      className: \"p-1\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 29,\n  columnNumber: 3\n}, this));\n_c4 = SelectContent;\nSelectContent.displayName = SelectPrimitive.Content.displayName;\nconst SelectItem = /*#__PURE__*/React.forwardRef(_c5 = ({\n  className,\n  children,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(SelectPrimitive.Item, {\n  ref: ref,\n  className: cn(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n  ...props,\n  children: [/*#__PURE__*/_jsxDEV(SelectPrimitive.ItemText, {\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(SelectPrimitive.ItemIndicator, {\n    className: \"absolute right-2 flex items-center\",\n    children: /*#__PURE__*/_jsxDEV(Check, {\n      className: \"h-4 w-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 47,\n  columnNumber: 3\n}, this));\n_c6 = SelectItem;\nSelectItem.displayName = SelectPrimitive.Item.displayName;\nexport { Select, SelectTrigger, SelectValue, SelectContent, SelectItem };\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"SelectTrigger$React.forwardRef\");\n$RefreshReg$(_c2, \"SelectTrigger\");\n$RefreshReg$(_c3, \"SelectContent$React.forwardRef\");\n$RefreshReg$(_c4, \"SelectContent\");\n$RefreshReg$(_c5, \"SelectItem$React.forwardRef\");\n$RefreshReg$(_c6, \"SelectItem\");", "map": {"version": 3, "names": ["React", "SelectPrimitive", "Check", "ChevronDown", "cn", "jsxDEV", "_jsxDEV", "Select", "Root", "SelectTrigger", "forwardRef", "_c", "className", "children", "props", "ref", "<PERSON><PERSON>", "Icon", "<PERSON><PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "SelectValue", "Value", "SelectContent", "_c3", "Portal", "Content", "Viewport", "_c4", "SelectItem", "_c5", "<PERSON><PERSON>", "ItemText", "ItemIndicator", "_c6", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/Select.js"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"../../lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectTrigger = React.forwardRef(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectContent = React.forwardRef(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-80\",\n        className\n      )}\n      {...props}\n    >\n      <SelectPrimitive.Viewport className=\"p-1\">\n        {children}\n      </SelectPrimitive.Viewport>\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectItem = React.forwardRef(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    <SelectPrimitive.ItemIndicator className=\"absolute right-2 flex items-center\">\n      <Check className=\"h-4 w-4\" />\n    </SelectPrimitive.ItemIndicator>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nexport { Select, SelectTrigger, SelectValue, SelectContent, SelectItem }\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,eAAe,MAAM,wBAAwB;AACzD,SAASC,KAAK,EAAEC,WAAW,QAAQ,cAAc;AAEjD,SAASC,EAAE,QAAQ,iBAAiB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,MAAM,GAAGN,eAAe,CAACO,IAAI;AAEnC,MAAMC,aAAa,gBAAGT,KAAK,CAACU,UAAU,CAAAC,EAAA,GAACA,CAAC;EAAEC,SAAS;EAAEC,QAAQ;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAC5ET,OAAA,CAACL,eAAe,CAACe,OAAO;EACtBD,GAAG,EAAEA,GAAI;EACTH,SAAS,EAAER,EAAE,CACX,2RAA2R,EAC3RQ,SACF,CAAE;EAAA,GACEE,KAAK;EAAAD,QAAA,GAERA,QAAQ,eACTP,OAAA,CAACL,eAAe,CAACgB,IAAI;IAACC,OAAO;IAAAL,QAAA,eAC3BP,OAAA,CAACH,WAAW;MAACS,SAAS,EAAC;IAAoB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC1B,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACA,CAC1B,CAAC;AAAAC,GAAA,GAdId,aAAa;AAenBA,aAAa,CAACe,WAAW,GAAGvB,eAAe,CAACe,OAAO,CAACQ,WAAW;AAE/D,MAAMC,WAAW,GAAGxB,eAAe,CAACyB,KAAK;AAEzC,MAAMC,aAAa,gBAAG3B,KAAK,CAACU,UAAU,CAAAkB,GAAA,GAACA,CAAC;EAAEhB,SAAS;EAAEC,QAAQ;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAC5ET,OAAA,CAACL,eAAe,CAAC4B,MAAM;EAAAhB,QAAA,eACrBP,OAAA,CAACL,eAAe,CAAC6B,OAAO;IACtBf,GAAG,EAAEA,GAAI;IACTH,SAAS,EAAER,EAAE,CACX,iIAAiI,EACjIQ,SACF,CAAE;IAAA,GACEE,KAAK;IAAAD,QAAA,eAETP,OAAA,CAACL,eAAe,CAAC8B,QAAQ;MAACnB,SAAS,EAAC,KAAK;MAAAC,QAAA,EACtCA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACe;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACJ,CACzB,CAAC;AAAAU,GAAA,GAfIL,aAAa;AAgBnBA,aAAa,CAACH,WAAW,GAAGvB,eAAe,CAAC6B,OAAO,CAACN,WAAW;AAE/D,MAAMS,UAAU,gBAAGjC,KAAK,CAACU,UAAU,CAAAwB,GAAA,GAACA,CAAC;EAAEtB,SAAS;EAAEC,QAAQ;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBACzET,OAAA,CAACL,eAAe,CAACkC,IAAI;EACnBpB,GAAG,EAAEA,GAAI;EACTH,SAAS,EAAER,EAAE,CACX,2NAA2N,EAC3NQ,SACF,CAAE;EAAA,GACEE,KAAK;EAAAD,QAAA,gBAETP,OAAA,CAACL,eAAe,CAACmC,QAAQ;IAAAvB,QAAA,EAAEA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAA2B,CAAC,eAC/DhB,OAAA,CAACL,eAAe,CAACoC,aAAa;IAACzB,SAAS,EAAC,oCAAoC;IAAAC,QAAA,eAC3EP,OAAA,CAACJ,KAAK;MAACU,SAAS,EAAC;IAAS;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACZ,CACvB,CAAC;AAAAgB,GAAA,GAdIL,UAAU;AAehBA,UAAU,CAACT,WAAW,GAAGvB,eAAe,CAACkC,IAAI,CAACX,WAAW;AAEzD,SAASjB,MAAM,EAAEE,aAAa,EAAEgB,WAAW,EAAEE,aAAa,EAAEM,UAAU;AAAE,IAAAtB,EAAA,EAAAY,GAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAA5B,EAAA;AAAA4B,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}