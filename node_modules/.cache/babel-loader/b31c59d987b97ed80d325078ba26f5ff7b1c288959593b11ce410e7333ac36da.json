{"ast": null, "code": "\"use client\";\n\n// src/popper.tsx\nimport * as React from \"react\";\nimport { useFloating, autoUpdate, offset, shift, limitShift, hide, arrow as floatingUIarrow, flip, size } from \"@floating-ui/react-dom\";\nimport * as ArrowPrimitive from \"@radix-ui/react-arrow\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { jsx } from \"react/jsx-runtime\";\nvar SIDE_OPTIONS = [\"top\", \"right\", \"bottom\", \"left\"];\nvar ALIGN_OPTIONS = [\"start\", \"center\", \"end\"];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = props => {\n  const {\n    __scopePopper,\n    children\n  } = props;\n  const [anchor, setAnchor] = React.useState(null);\n  return /* @__PURE__ */jsx(PopperProvider, {\n    scope: __scopePopper,\n    anchor,\n    onAnchorChange: setAnchor,\n    children\n  });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopePopper,\n    virtualRef,\n    ...anchorProps\n  } = props;\n  const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  React.useEffect(() => {\n    context.onAnchorChange(virtualRef?.current || ref.current);\n  });\n  return virtualRef ? null : /* @__PURE__ */jsx(Primitive.div, {\n    ...anchorProps,\n    ref: composedRefs\n  });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopePopper,\n    side = \"bottom\",\n    sideOffset = 0,\n    align = \"center\",\n    alignOffset = 0,\n    arrowPadding = 0,\n    avoidCollisions = true,\n    collisionBoundary = [],\n    collisionPadding: collisionPaddingProp = 0,\n    sticky = \"partial\",\n    hideWhenDetached = false,\n    updatePositionStrategy = \"optimized\",\n    onPlaced,\n    ...contentProps\n  } = props;\n  const context = usePopperContext(CONTENT_NAME, __scopePopper);\n  const [content, setContent] = React.useState(null);\n  const composedRefs = useComposedRefs(forwardedRef, node => setContent(node));\n  const [arrow, setArrow] = React.useState(null);\n  const arrowSize = useSize(arrow);\n  const arrowWidth = arrowSize?.width ?? 0;\n  const arrowHeight = arrowSize?.height ?? 0;\n  const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n  const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...collisionPaddingProp\n  };\n  const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n  const hasExplicitBoundaries = boundary.length > 0;\n  const detectOverflowOptions = {\n    padding: collisionPadding,\n    boundary: boundary.filter(isNotNull),\n    // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n    altBoundary: hasExplicitBoundaries\n  };\n  const {\n    refs,\n    floatingStyles,\n    placement,\n    isPositioned,\n    middlewareData\n  } = useFloating({\n    // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n    strategy: \"fixed\",\n    placement: desiredPlacement,\n    whileElementsMounted: (...args) => {\n      const cleanup = autoUpdate(...args, {\n        animationFrame: updatePositionStrategy === \"always\"\n      });\n      return cleanup;\n    },\n    elements: {\n      reference: context.anchor\n    },\n    middleware: [offset({\n      mainAxis: sideOffset + arrowHeight,\n      alignmentAxis: alignOffset\n    }), avoidCollisions && shift({\n      mainAxis: true,\n      crossAxis: false,\n      limiter: sticky === \"partial\" ? limitShift() : void 0,\n      ...detectOverflowOptions\n    }), avoidCollisions && flip({\n      ...detectOverflowOptions\n    }), size({\n      ...detectOverflowOptions,\n      apply: ({\n        elements,\n        rects,\n        availableWidth,\n        availableHeight\n      }) => {\n        const {\n          width: anchorWidth,\n          height: anchorHeight\n        } = rects.reference;\n        const contentStyle = elements.floating.style;\n        contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n        contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n        contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n        contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n      }\n    }), arrow && floatingUIarrow({\n      element: arrow,\n      padding: arrowPadding\n    }), transformOrigin({\n      arrowWidth,\n      arrowHeight\n    }), hideWhenDetached && hide({\n      strategy: \"referenceHidden\",\n      ...detectOverflowOptions\n    })]\n  });\n  const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n  const handlePlaced = useCallbackRef(onPlaced);\n  useLayoutEffect(() => {\n    if (isPositioned) {\n      handlePlaced?.();\n    }\n  }, [isPositioned, handlePlaced]);\n  const arrowX = middlewareData.arrow?.x;\n  const arrowY = middlewareData.arrow?.y;\n  const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n  const [contentZIndex, setContentZIndex] = React.useState();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n  return /* @__PURE__ */jsx(\"div\", {\n    ref: refs.setFloating,\n    \"data-radix-popper-content-wrapper\": \"\",\n    style: {\n      ...floatingStyles,\n      transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n      // keep off the page when measuring\n      minWidth: \"max-content\",\n      zIndex: contentZIndex,\n      [\"--radix-popper-transform-origin\"]: [middlewareData.transformOrigin?.x, middlewareData.transformOrigin?.y].join(\" \"),\n      // hide the content if using the hide middleware and should be hidden\n      // set visibility to hidden and disable pointer events so the UI behaves\n      // as if the PopperContent isn't there at all\n      ...(middlewareData.hide?.referenceHidden && {\n        visibility: \"hidden\",\n        pointerEvents: \"none\"\n      })\n    },\n    dir: props.dir,\n    children: /* @__PURE__ */jsx(PopperContentProvider, {\n      scope: __scopePopper,\n      placedSide,\n      onArrowChange: setArrow,\n      arrowX,\n      arrowY,\n      shouldHideArrow: cannotCenterArrow,\n      children: /* @__PURE__ */jsx(Primitive.div, {\n        \"data-side\": placedSide,\n        \"data-align\": placedAlign,\n        ...contentProps,\n        ref: composedRefs,\n        style: {\n          ...contentProps.style,\n          // if the PopperContent hasn't been placed yet (not all measurements done)\n          // we prevent animations so that users's animation don't kick in too early referring wrong sides\n          animation: !isPositioned ? \"none\" : void 0\n        }\n      })\n    })\n  });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n  top: \"bottom\",\n  right: \"left\",\n  bottom: \"top\",\n  left: \"right\"\n};\nvar PopperArrow = React.forwardRef(function PopperArrow2(props, forwardedRef) {\n  const {\n    __scopePopper,\n    ...arrowProps\n  } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */\n    jsx(\"span\", {\n      ref: contentContext.onArrowChange,\n      style: {\n        position: \"absolute\",\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: \"\",\n          right: \"0 0\",\n          bottom: \"center 0\",\n          left: \"100% 0\"\n        }[contentContext.placedSide],\n        transform: {\n          top: \"translateY(100%)\",\n          right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n          bottom: `rotate(180deg)`,\n          left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n      },\n      children: /* @__PURE__ */jsx(ArrowPrimitive.Root, {\n        ...arrowProps,\n        ref: forwardedRef,\n        style: {\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: \"block\"\n        }\n      })\n    })\n  );\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n  return value !== null;\n}\nvar transformOrigin = options => ({\n  name: \"transformOrigin\",\n  options,\n  fn(data) {\n    const {\n      placement,\n      rects,\n      middlewareData\n    } = data;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = {\n      start: \"0%\",\n      center: \"50%\",\n      end: \"100%\"\n    }[placedAlign];\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n    let x = \"\";\n    let y = \"\";\n    if (placedSide === \"bottom\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === \"top\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === \"right\") {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === \"left\") {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return {\n      data: {\n        x,\n        y\n      }\n    };\n  }\n});\nfunction getSideAndAlignFromPlacement(placement) {\n  const [side, align = \"center\"] = placement.split(\"-\");\n  return [side, align];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\nexport { ALIGN_OPTIONS, Anchor, Arrow, Content, Popper, PopperAnchor, PopperArrow, PopperContent, Root2 as Root, SIDE_OPTIONS, createPopperScope };", "map": {"version": 3, "names": ["React", "useFloating", "autoUpdate", "offset", "shift", "limitShift", "hide", "arrow", "floatingUIarrow", "flip", "size", "ArrowPrimitive", "useComposedRefs", "createContextScope", "Primitive", "useCallbackRef", "useLayoutEffect", "useSize", "jsx", "SIDE_OPTIONS", "ALIGN_OPTIONS", "POPPER_NAME", "createPopperContext", "createPopperScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePopperContext", "<PERSON><PERSON>", "props", "__scope<PERSON>opper", "children", "anchor", "setAnchor", "useState", "scope", "onAnchorChange", "displayName", "ANCHOR_NAME", "PopperA<PERSON><PERSON>", "forwardRef", "forwardedRef", "virtualRef", "anchorProps", "context", "ref", "useRef", "composedRefs", "useEffect", "current", "div", "CONTENT_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useContentContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "side", "sideOffset", "align", "alignOffset", "arrowPadding", "avoidCollisions", "collisionBoundary", "collisionPadding", "collisionPaddingProp", "sticky", "hideWhenDetached", "updatePositionStrategy", "onPlaced", "contentProps", "content", "<PERSON><PERSON><PERSON><PERSON>", "node", "setArrow", "arrowSize", "arrow<PERSON>idth", "width", "arrowHeight", "height", "desiredPlacement", "top", "right", "bottom", "left", "boundary", "Array", "isArray", "hasExplicitBoundaries", "length", "detectOverflowOptions", "padding", "filter", "isNotNull", "altBoundary", "refs", "floatingStyles", "placement", "isPositioned", "middlewareData", "strategy", "whileElementsMounted", "args", "cleanup", "animationFrame", "elements", "reference", "middleware", "mainAxis", "alignmentAxis", "crossAxis", "limiter", "apply", "rects", "availableWidth", "availableHeight", "anchorWidth", "anchorHeight", "contentStyle", "floating", "style", "setProperty", "element", "transform<PERSON><PERSON>in", "placedSide", "placedAlign", "getSideAndAlignFromPlacement", "handlePlaced", "arrowX", "x", "arrowY", "y", "cannotCenterArrow", "centerOffset", "contentZIndex", "setContentZIndex", "window", "getComputedStyle", "zIndex", "setFloating", "transform", "min<PERSON><PERSON><PERSON>", "join", "referenceHidden", "visibility", "pointerEvents", "dir", "onArrowChange", "shouldHideArrow", "animation", "ARROW_NAME", "OPPOSITE_SIDE", "PopperArrow", "PopperArrow2", "arrowProps", "contentContext", "baseSide", "position", "Root", "display", "value", "options", "name", "fn", "data", "isArrowHidden", "noArrowAlign", "start", "center", "end", "arrowXCenter", "arrowYCenter", "split", "Root2", "<PERSON><PERSON>", "Content", "Arrow"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/@radix-ui/react-popper/src/popper.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ComponentRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n"], "mappings": ";;;AAAA,YAAYA,KAAA,MAAW;AACvB,SACEC,WAAA,EACAC,UAAA,EACAC,MAAA,EACAC,KAAA,EACAC,UAAA,EACAC,IAAA,EACAC,KAAA,IAASC,eAAA,EACTC,IAAA,EACAC,IAAA,QACK;AACP,YAAYC,cAAA,MAAoB;AAChC,SAASC,eAAA,QAAuB;AAChC,SAASC,kBAAA,QAA0B;AACnC,SAASC,SAAA,QAAiB;AAC1B,SAASC,cAAA,QAAsB;AAC/B,SAASC,eAAA,QAAuB;AAChC,SAASC,OAAA,QAAe;AAkCpB,SAAAC,GAAA;AA5BJ,IAAMC,YAAA,GAAe,CAAC,OAAO,SAAS,UAAU,MAAM;AACtD,IAAMC,aAAA,GAAgB,CAAC,SAAS,UAAU,KAAK;AAS/C,IAAMC,WAAA,GAAc;AAGpB,IAAM,CAACC,mBAAA,EAAqBC,iBAAiB,IAAIV,kBAAA,CAAmBQ,WAAW;AAM/E,IAAM,CAACG,cAAA,EAAgBC,gBAAgB,IAAIH,mBAAA,CAAwCD,WAAW;AAK9F,IAAMK,MAAA,GAAiCC,KAAA,IAAoC;EACzE,MAAM;IAAEC,aAAA;IAAeC;EAAS,IAAIF,KAAA;EACpC,MAAM,CAACG,MAAA,EAAQC,SAAS,IAAU/B,KAAA,CAAAgC,QAAA,CAA4B,IAAI;EAClE,OACE,eAAAd,GAAA,CAACM,cAAA;IAAeS,KAAA,EAAOL,aAAA;IAAeE,MAAA;IAAgBI,cAAA,EAAgBH,SAAA;IACnEF;EAAA,CACH;AAEJ;AAEAH,MAAA,CAAOS,WAAA,GAAcd,WAAA;AAMrB,IAAMe,WAAA,GAAc;AAQpB,IAAMC,YAAA,GAAqBrC,KAAA,CAAAsC,UAAA,CACzB,CAACX,KAAA,EAAuCY,YAAA,KAAiB;EACvD,MAAM;IAAEX,aAAA;IAAeY,UAAA;IAAY,GAAGC;EAAY,IAAId,KAAA;EACtD,MAAMe,OAAA,GAAUjB,gBAAA,CAAiBW,WAAA,EAAaR,aAAa;EAC3D,MAAMe,GAAA,GAAY3C,KAAA,CAAA4C,MAAA,CAA4B,IAAI;EAClD,MAAMC,YAAA,GAAejC,eAAA,CAAgB2B,YAAA,EAAcI,GAAG;EAEhD3C,KAAA,CAAA8C,SAAA,CAAU,MAAM;IAIpBJ,OAAA,CAAQR,cAAA,CAAeM,UAAA,EAAYO,OAAA,IAAWJ,GAAA,CAAII,OAAO;EAC3D,CAAC;EAED,OAAOP,UAAA,GAAa,OAAO,eAAAtB,GAAA,CAACJ,SAAA,CAAUkC,GAAA,EAAV;IAAe,GAAGP,WAAA;IAAaE,GAAA,EAAKE;EAAA,CAAc;AAChF,CACF;AAEAR,YAAA,CAAaF,WAAA,GAAcC,WAAA;AAM3B,IAAMa,YAAA,GAAe;AAUrB,IAAM,CAACC,qBAAA,EAAuBC,iBAAiB,IAC7C7B,mBAAA,CAA+C2B,YAAY;AAoB7D,IAAMG,aAAA,GAAsBpD,KAAA,CAAAsC,UAAA,CAC1B,CAACX,KAAA,EAAwCY,YAAA,KAAiB;EACxD,MAAM;IACJX,aAAA;IACAyB,IAAA,GAAO;IACPC,UAAA,GAAa;IACbC,KAAA,GAAQ;IACRC,WAAA,GAAc;IACdC,YAAA,GAAe;IACfC,eAAA,GAAkB;IAClBC,iBAAA,GAAoB,EAAC;IACrBC,gBAAA,EAAkBC,oBAAA,GAAuB;IACzCC,MAAA,GAAS;IACTC,gBAAA,GAAmB;IACnBC,sBAAA,GAAyB;IACzBC,QAAA;IACA,GAAGC;EACL,IAAIvC,KAAA;EAEJ,MAAMe,OAAA,GAAUjB,gBAAA,CAAiBwB,YAAA,EAAcrB,aAAa;EAE5D,MAAM,CAACuC,OAAA,EAASC,UAAU,IAAUpE,KAAA,CAAAgC,QAAA,CAAgC,IAAI;EACxE,MAAMa,YAAA,GAAejC,eAAA,CAAgB2B,YAAA,EAAe8B,IAAA,IAASD,UAAA,CAAWC,IAAI,CAAC;EAE7E,MAAM,CAAC9D,KAAA,EAAO+D,QAAQ,IAAUtE,KAAA,CAAAgC,QAAA,CAAiC,IAAI;EACrE,MAAMuC,SAAA,GAAYtD,OAAA,CAAQV,KAAK;EAC/B,MAAMiE,UAAA,GAAaD,SAAA,EAAWE,KAAA,IAAS;EACvC,MAAMC,WAAA,GAAcH,SAAA,EAAWI,MAAA,IAAU;EAEzC,MAAMC,gBAAA,GAAoBvB,IAAA,IAAQE,KAAA,KAAU,WAAW,MAAMA,KAAA,GAAQ;EAErE,MAAMK,gBAAA,GACJ,OAAOC,oBAAA,KAAyB,WAC5BA,oBAAA,GACA;IAAEgB,GAAA,EAAK;IAAGC,KAAA,EAAO;IAAGC,MAAA,EAAQ;IAAGC,IAAA,EAAM;IAAG,GAAGnB;EAAqB;EAEtE,MAAMoB,QAAA,GAAWC,KAAA,CAAMC,OAAA,CAAQxB,iBAAiB,IAAIA,iBAAA,GAAoB,CAACA,iBAAiB;EAC1F,MAAMyB,qBAAA,GAAwBH,QAAA,CAASI,MAAA,GAAS;EAEhD,MAAMC,qBAAA,GAAwB;IAC5BC,OAAA,EAAS3B,gBAAA;IACTqB,QAAA,EAAUA,QAAA,CAASO,MAAA,CAAOC,SAAS;IAAA;IAEnCC,WAAA,EAAaN;EACf;EAEA,MAAM;IAAEO,IAAA;IAAMC,cAAA;IAAgBC,SAAA;IAAWC,YAAA;IAAcC;EAAe,IAAI9F,WAAA,CAAY;IAAA;IAEpF+F,QAAA,EAAU;IACVH,SAAA,EAAWjB,gBAAA;IACXqB,oBAAA,EAAsBA,CAAA,GAAIC,IAAA,KAAS;MACjC,MAAMC,OAAA,GAAUjG,UAAA,CAAW,GAAGgG,IAAA,EAAM;QAClCE,cAAA,EAAgBpC,sBAAA,KAA2B;MAC7C,CAAC;MACD,OAAOmC,OAAA;IACT;IACAE,QAAA,EAAU;MACRC,SAAA,EAAW5D,OAAA,CAAQZ;IACrB;IACAyE,UAAA,EAAY,CACVpG,MAAA,CAAO;MAAEqG,QAAA,EAAUlD,UAAA,GAAaoB,WAAA;MAAa+B,aAAA,EAAejD;IAAY,CAAC,GACzEE,eAAA,IACEtD,KAAA,CAAM;MACJoG,QAAA,EAAU;MACVE,SAAA,EAAW;MACXC,OAAA,EAAS7C,MAAA,KAAW,YAAYzD,UAAA,CAAW,IAAI;MAC/C,GAAGiF;IACL,CAAC,GACH5B,eAAA,IAAmBjD,IAAA,CAAK;MAAE,GAAG6E;IAAsB,CAAC,GACpD5E,IAAA,CAAK;MACH,GAAG4E,qBAAA;MACHsB,KAAA,EAAOA,CAAC;QAAEP,QAAA;QAAUQ,KAAA;QAAOC,cAAA;QAAgBC;MAAgB,MAAM;QAC/D,MAAM;UAAEtC,KAAA,EAAOuC,WAAA;UAAarC,MAAA,EAAQsC;QAAa,IAAIJ,KAAA,CAAMP,SAAA;QAC3D,MAAMY,YAAA,GAAeb,QAAA,CAASc,QAAA,CAASC,KAAA;QACvCF,YAAA,CAAaG,WAAA,CAAY,kCAAkC,GAAGP,cAAc,IAAI;QAChFI,YAAA,CAAaG,WAAA,CAAY,mCAAmC,GAAGN,eAAe,IAAI;QAClFG,YAAA,CAAaG,WAAA,CAAY,+BAA+B,GAAGL,WAAW,IAAI;QAC1EE,YAAA,CAAaG,WAAA,CAAY,gCAAgC,GAAGJ,YAAY,IAAI;MAC9E;IACF,CAAC,GACD1G,KAAA,IAASC,eAAA,CAAgB;MAAE8G,OAAA,EAAS/G,KAAA;MAAOgF,OAAA,EAAS9B;IAAa,CAAC,GAClE8D,eAAA,CAAgB;MAAE/C,UAAA;MAAYE;IAAY,CAAC,GAC3CX,gBAAA,IAAoBzD,IAAA,CAAK;MAAE0F,QAAA,EAAU;MAAmB,GAAGV;IAAsB,CAAC;EAEtF,CAAC;EAED,MAAM,CAACkC,UAAA,EAAYC,WAAW,IAAIC,4BAAA,CAA6B7B,SAAS;EAExE,MAAM8B,YAAA,GAAe5G,cAAA,CAAekD,QAAQ;EAC5CjD,eAAA,CAAgB,MAAM;IACpB,IAAI8E,YAAA,EAAc;MAChB6B,YAAA,GAAe;IACjB;EACF,GAAG,CAAC7B,YAAA,EAAc6B,YAAY,CAAC;EAE/B,MAAMC,MAAA,GAAS7B,cAAA,CAAexF,KAAA,EAAOsH,CAAA;EACrC,MAAMC,MAAA,GAAS/B,cAAA,CAAexF,KAAA,EAAOwH,CAAA;EACrC,MAAMC,iBAAA,GAAoBjC,cAAA,CAAexF,KAAA,EAAO0H,YAAA,KAAiB;EAEjE,MAAM,CAACC,aAAA,EAAeC,gBAAgB,IAAUnI,KAAA,CAAAgC,QAAA,CAAiB;EACjEhB,eAAA,CAAgB,MAAM;IACpB,IAAImD,OAAA,EAASgE,gBAAA,CAAiBC,MAAA,CAAOC,gBAAA,CAAiBlE,OAAO,EAAEmE,MAAM;EACvE,GAAG,CAACnE,OAAO,CAAC;EAEZ,OACE,eAAAjD,GAAA,CAAC;IACCyB,GAAA,EAAKgD,IAAA,CAAK4C,WAAA;IACV,qCAAkC;IAClCnB,KAAA,EAAO;MACL,GAAGxB,cAAA;MACH4C,SAAA,EAAW1C,YAAA,GAAeF,cAAA,CAAe4C,SAAA,GAAY;MAAA;MACrDC,QAAA,EAAU;MACVH,MAAA,EAAQJ,aAAA;MACR,CAAC,iCAAwC,GAAG,CAC1CnC,cAAA,CAAewB,eAAA,EAAiBM,CAAA,EAChC9B,cAAA,CAAewB,eAAA,EAAiBQ,CAAA,CAClC,CAAEW,IAAA,CAAK,GAAG;MAAA;MAAA;MAAA;MAKV,IAAI3C,cAAA,CAAezF,IAAA,EAAMqI,eAAA,IAAmB;QAC1CC,UAAA,EAAY;QACZC,aAAA,EAAe;MACjB;IACF;IAIAC,GAAA,EAAKnH,KAAA,CAAMmH,GAAA;IAEXjH,QAAA,iBAAAX,GAAA,CAACgC,qBAAA;MACCjB,KAAA,EAAOL,aAAA;MACP4F,UAAA;MACAuB,aAAA,EAAezE,QAAA;MACfsD,MAAA;MACAE,MAAA;MACAkB,eAAA,EAAiBhB,iBAAA;MAEjBnG,QAAA,iBAAAX,GAAA,CAACJ,SAAA,CAAUkC,GAAA,EAAV;QACC,aAAWwE,UAAA;QACX,cAAYC,WAAA;QACX,GAAGvD,YAAA;QACJvB,GAAA,EAAKE,YAAA;QACLuE,KAAA,EAAO;UACL,GAAGlD,YAAA,CAAakD,KAAA;UAAA;UAAA;UAGhB6B,SAAA,EAAW,CAACnD,YAAA,GAAe,SAAS;QACtC;MAAA,CACF;IAAA,CACF;EAAA,CACF;AAEJ,CACF;AAEA1C,aAAA,CAAcjB,WAAA,GAAcc,YAAA;AAM5B,IAAMiG,UAAA,GAAa;AAEnB,IAAMC,aAAA,GAAoC;EACxCtE,GAAA,EAAK;EACLC,KAAA,EAAO;EACPC,MAAA,EAAQ;EACRC,IAAA,EAAM;AACR;AAMA,IAAMoE,WAAA,GAAoBpJ,KAAA,CAAAsC,UAAA,CAAiD,SAAS+G,aAClF1H,KAAA,EACAY,YAAA,EACA;EACA,MAAM;IAAEX,aAAA;IAAe,GAAG0H;EAAW,IAAI3H,KAAA;EACzC,MAAM4H,cAAA,GAAiBpG,iBAAA,CAAkB+F,UAAA,EAAYtH,aAAa;EAClE,MAAM4H,QAAA,GAAWL,aAAA,CAAcI,cAAA,CAAe/B,UAAU;EAExD;IAAA;IAAA;IAAA;IAIE;IAAAtG,GAAA,CAAC;MACCyB,GAAA,EAAK4G,cAAA,CAAeR,aAAA;MACpB3B,KAAA,EAAO;QACLqC,QAAA,EAAU;QACVzE,IAAA,EAAMuE,cAAA,CAAe3B,MAAA;QACrB/C,GAAA,EAAK0E,cAAA,CAAezB,MAAA;QACpB,CAAC0B,QAAQ,GAAG;QACZjC,eAAA,EAAiB;UACf1C,GAAA,EAAK;UACLC,KAAA,EAAO;UACPC,MAAA,EAAQ;UACRC,IAAA,EAAM;QACR,EAAEuE,cAAA,CAAe/B,UAAU;QAC3BgB,SAAA,EAAW;UACT3D,GAAA,EAAK;UACLC,KAAA,EAAO;UACPC,MAAA,EAAQ;UACRC,IAAA,EAAM;QACR,EAAEuE,cAAA,CAAe/B,UAAU;QAC3BoB,UAAA,EAAYW,cAAA,CAAeP,eAAA,GAAkB,WAAW;MAC1D;MAEAnH,QAAA,iBAAAX,GAAA,CAAgBP,cAAA,CAAA+I,IAAA,EAAf;QACE,GAAGJ,UAAA;QACJ3G,GAAA,EAAKJ,YAAA;QACL6E,KAAA,EAAO;UACL,GAAGkC,UAAA,CAAWlC,KAAA;UAAA;UAEduC,OAAA,EAAS;QACX;MAAA,CACF;IAAA,CACF;EAAA;AAEJ,CAAC;AAEDP,WAAA,CAAYjH,WAAA,GAAc+G,UAAA;AAI1B,SAASzD,UAAamE,KAAA,EAA6B;EACjD,OAAOA,KAAA,KAAU;AACnB;AAEA,IAAMrC,eAAA,GAAmBsC,OAAA,KAAsE;EAC7FC,IAAA,EAAM;EACND,OAAA;EACAE,GAAGC,IAAA,EAAM;IACP,MAAM;MAAEnE,SAAA;MAAWgB,KAAA;MAAOd;IAAe,IAAIiE,IAAA;IAE7C,MAAMhC,iBAAA,GAAoBjC,cAAA,CAAexF,KAAA,EAAO0H,YAAA,KAAiB;IACjE,MAAMgC,aAAA,GAAgBjC,iBAAA;IACtB,MAAMxD,UAAA,GAAayF,aAAA,GAAgB,IAAIJ,OAAA,CAAQrF,UAAA;IAC/C,MAAME,WAAA,GAAcuF,aAAA,GAAgB,IAAIJ,OAAA,CAAQnF,WAAA;IAEhD,MAAM,CAAC8C,UAAA,EAAYC,WAAW,IAAIC,4BAAA,CAA6B7B,SAAS;IACxE,MAAMqE,YAAA,GAAe;MAAEC,KAAA,EAAO;MAAMC,MAAA,EAAQ;MAAOC,GAAA,EAAK;IAAO,EAAE5C,WAAW;IAE5E,MAAM6C,YAAA,IAAgBvE,cAAA,CAAexF,KAAA,EAAOsH,CAAA,IAAK,KAAKrD,UAAA,GAAa;IACnE,MAAM+F,YAAA,IAAgBxE,cAAA,CAAexF,KAAA,EAAOwH,CAAA,IAAK,KAAKrD,WAAA,GAAc;IAEpE,IAAImD,CAAA,GAAI;IACR,IAAIE,CAAA,GAAI;IAER,IAAIP,UAAA,KAAe,UAAU;MAC3BK,CAAA,GAAIoC,aAAA,GAAgBC,YAAA,GAAe,GAAGI,YAAY;MAClDvC,CAAA,GAAI,GAAG,CAACrD,WAAW;IACrB,WAAW8C,UAAA,KAAe,OAAO;MAC/BK,CAAA,GAAIoC,aAAA,GAAgBC,YAAA,GAAe,GAAGI,YAAY;MAClDvC,CAAA,GAAI,GAAGlB,KAAA,CAAMM,QAAA,CAASxC,MAAA,GAASD,WAAW;IAC5C,WAAW8C,UAAA,KAAe,SAAS;MACjCK,CAAA,GAAI,GAAG,CAACnD,WAAW;MACnBqD,CAAA,GAAIkC,aAAA,GAAgBC,YAAA,GAAe,GAAGK,YAAY;IACpD,WAAW/C,UAAA,KAAe,QAAQ;MAChCK,CAAA,GAAI,GAAGhB,KAAA,CAAMM,QAAA,CAAS1C,KAAA,GAAQC,WAAW;MACzCqD,CAAA,GAAIkC,aAAA,GAAgBC,YAAA,GAAe,GAAGK,YAAY;IACpD;IACA,OAAO;MAAEP,IAAA,EAAM;QAAEnC,CAAA;QAAGE;MAAE;IAAE;EAC1B;AACF;AAEA,SAASL,6BAA6B7B,SAAA,EAAsB;EAC1D,MAAM,CAACxC,IAAA,EAAME,KAAA,GAAQ,QAAQ,IAAIsC,SAAA,CAAU2E,KAAA,CAAM,GAAG;EACpD,OAAO,CAACnH,IAAA,EAAcE,KAAc;AACtC;AAEA,IAAMkH,KAAA,GAAO/I,MAAA;AACb,IAAMgJ,MAAA,GAASrI,YAAA;AACf,IAAMsI,OAAA,GAAUvH,aAAA;AAChB,IAAMwH,KAAA,GAAQxB,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}