{"ast": null, "code": "// import { clsx } from \"clsx\";\n\n// export function cn(...inputs) {\n//   return clsx(inputs);\n// }", "map": {"version": 3, "names": [], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/lib/utils.js"], "sourcesContent": ["// import { clsx } from \"clsx\";\n\n// export function cn(...inputs) {\n//   return clsx(inputs);\n// }\n"], "mappings": "AAAA;;AAEA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}