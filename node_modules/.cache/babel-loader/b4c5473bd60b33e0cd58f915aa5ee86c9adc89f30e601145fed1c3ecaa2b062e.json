{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10.852 19.772-.383.924\",\n  key: \"r7sl7d\"\n}], [\"path\", {\n  d: \"m13.148 14.228.383-.923\",\n  key: \"1d5zpm\"\n}], [\"path\", {\n  d: \"M13.148 19.772a3 3 0 1 0-2.296-5.544l-.383-.923\",\n  key: \"1ydik7\"\n}], [\"path\", {\n  d: \"m13.53 20.696-.382-.924a3 3 0 1 1-2.296-5.544\",\n  key: \"1m1vsf\"\n}], [\"path\", {\n  d: \"m14.772 15.852.923-.383\",\n  key: \"660p6e\"\n}], [\"path\", {\n  d: \"m14.772 18.148.923.383\",\n  key: \"hrcpis\"\n}], [\"path\", {\n  d: \"M4.2 15.1a7 7 0 1 1 9.93-9.858A7 7 0 0 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2\",\n  key: \"j2q98n\"\n}], [\"path\", {\n  d: \"m9.228 15.852-.923-.383\",\n  key: \"1p9ong\"\n}], [\"path\", {\n  d: \"m9.228 18.148-.923.383\",\n  key: \"6558rz\"\n}]];\nconst CloudCog = createLucideIcon(\"cloud-cog\", __iconNode);\nexport { __iconNode, CloudCog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "CloudCog", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/cloud-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm10.852 19.772-.383.924', key: 'r7sl7d' }],\n  ['path', { d: 'm13.148 14.228.383-.923', key: '1d5zpm' }],\n  ['path', { d: 'M13.148 19.772a3 3 0 1 0-2.296-5.544l-.383-.923', key: '1ydik7' }],\n  ['path', { d: 'm13.53 20.696-.382-.924a3 3 0 1 1-2.296-5.544', key: '1m1vsf' }],\n  ['path', { d: 'm14.772 15.852.923-.383', key: '660p6e' }],\n  ['path', { d: 'm14.772 18.148.923.383', key: 'hrcpis' }],\n  [\n    'path',\n    {\n      d: 'M4.2 15.1a7 7 0 1 1 9.93-9.858A7 7 0 0 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.2',\n      key: 'j2q98n',\n    },\n  ],\n  ['path', { d: 'm9.228 15.852-.923-.383', key: '1p9ong' }],\n  ['path', { d: 'm9.228 18.148-.923.383', key: '6558rz' }],\n];\n\n/**\n * @component @name CloudCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAuODUyIDE5Ljc3Mi0uMzgzLjkyNCIgLz4KICA8cGF0aCBkPSJtMTMuMTQ4IDE0LjIyOC4zODMtLjkyMyIgLz4KICA8cGF0aCBkPSJNMTMuMTQ4IDE5Ljc3MmEzIDMgMCAxIDAtMi4yOTYtNS41NDRsLS4zODMtLjkyMyIgLz4KICA8cGF0aCBkPSJtMTMuNTMgMjAuNjk2LS4zODItLjkyNGEzIDMgMCAxIDEtMi4yOTYtNS41NDQiIC8+CiAgPHBhdGggZD0ibTE0Ljc3MiAxNS44NTIuOTIzLS4zODMiIC8+CiAgPHBhdGggZD0ibTE0Ljc3MiAxOC4xNDguOTIzLjM4MyIgLz4KICA8cGF0aCBkPSJNNC4yIDE1LjFhNyA3IDAgMSAxIDkuOTMtOS44NThBNyA3IDAgMCAxIDE1LjcxIDhoMS43OWE0LjUgNC41IDAgMCAxIDIuNSA4LjIiIC8+CiAgPHBhdGggZD0ibTkuMjI4IDE1Ljg1Mi0uOTIzLS4zODMiIC8+CiAgPHBhdGggZD0ibTkuMjI4IDE4LjE0OC0uOTIzLjM4MyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/cloud-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CloudCog = createLucideIcon('cloud-cog', __iconNode);\n\nexport default CloudCog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChF,CAAC,MAAQ;EAAED,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAU,GACzD;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}