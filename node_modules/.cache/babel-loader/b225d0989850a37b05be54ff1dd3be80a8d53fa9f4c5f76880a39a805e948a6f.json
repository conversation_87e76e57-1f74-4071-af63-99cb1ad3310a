{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/Select.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Select = ({\n  value,\n  onChange,\n  children,\n  className\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"select\", {\n    value: value,\n    onChange: onChange,\n    className: `border border-gray-300 rounded px-3 py-2 w-full ${className}`,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Select;\nexport const SelectItem = ({\n  value,\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"option\", {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 10\n  }, this);\n};\n_c2 = SelectItem;\nvar _c, _c2;\n$RefreshReg$(_c, \"Select\");\n$RefreshReg$(_c2, \"SelectItem\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Select", "value", "onChange", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "SelectItem", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/src/components/UI/Select.js"], "sourcesContent": ["import React from \"react\";\n\nexport const Select = ({ value, onChange, children, className }) => {\n  return (\n    <select\n      value={value}\n      onChange={onChange}\n      className={`border border-gray-300 rounded px-3 py-2 w-full ${className}`}\n    >\n      {children}\n    </select>\n  );\n};\n\nexport const SelectItem = ({ value, children }) => {\n  return <option value={value}>{children}</option>;\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,OAAO,MAAMC,MAAM,GAAGA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAClE,oBACEL,OAAA;IACEE,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA,QAAS;IACnBE,SAAS,EAAE,mDAAmDA,SAAS,EAAG;IAAAD,QAAA,EAEzEA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACC,EAAA,GAVWT,MAAM;AAYnB,OAAO,MAAMU,UAAU,GAAGA,CAAC;EAAET,KAAK;EAAEE;AAAS,CAAC,KAAK;EACjD,oBAAOJ,OAAA;IAAQE,KAAK,EAAEA,KAAM;IAAAE,QAAA,EAAEA;EAAQ;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAS,CAAC;AAClD,CAAC;AAACG,GAAA,GAFWD,UAAU;AAAA,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}