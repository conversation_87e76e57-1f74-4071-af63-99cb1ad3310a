{"ast": null, "code": "// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\nimport * as React from \"react\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n  React.useEffect(() => {\n    const handleKeyDown = event => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, {\n      capture: true\n    });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, {\n      capture: true\n    });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\nexport { useEscapeKeydown };", "map": {"version": 3, "names": ["React", "useCallbackRef", "useEscapeKeydown", "onEscapeKeyDownProp", "ownerDocument", "globalThis", "document", "onEscapeKeyDown", "useEffect", "handleKeyDown", "event", "key", "addEventListener", "capture", "removeEventListener"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/@radix-ui/react-use-escape-keydown/src/use-escape-keydown.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AACvB,SAASC,cAAA,QAAsB;AAK/B,SAASC,iBACPC,mBAAA,EACAC,aAAA,GAA0BC,UAAA,EAAYC,QAAA,EACtC;EACA,MAAMC,eAAA,GAAkBN,cAAA,CAAeE,mBAAmB;EAEpDH,KAAA,CAAAQ,SAAA,CAAU,MAAM;IACpB,MAAMC,aAAA,GAAiBC,KAAA,IAAyB;MAC9C,IAAIA,KAAA,CAAMC,GAAA,KAAQ,UAAU;QAC1BJ,eAAA,CAAgBG,KAAK;MACvB;IACF;IACAN,aAAA,CAAcQ,gBAAA,CAAiB,WAAWH,aAAA,EAAe;MAAEI,OAAA,EAAS;IAAK,CAAC;IAC1E,OAAO,MAAMT,aAAA,CAAcU,mBAAA,CAAoB,WAAWL,aAAA,EAAe;MAAEI,OAAA,EAAS;IAAK,CAAC;EAC5F,GAAG,CAACN,eAAA,EAAiBH,aAAa,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}