{"ast": null, "code": "import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n  return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) {\n  return [event.deltaX, event.deltaY];\n};\nvar extractRef = function (ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) {\n  return x[0] === y[0] && x[1] === y[1];\n};\nvar generateStyle = function (id) {\n  return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\");\n};\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n  var shouldPreventQueue = React.useRef([]);\n  var touchStartRef = React.useRef([0, 0]);\n  var activeAxis = React.useRef();\n  var id = React.useState(idCounter++)[0];\n  var Style = React.useState(styleSingleton)[0];\n  var lastProps = React.useRef(props);\n  React.useEffect(function () {\n    lastProps.current = props;\n  }, [props]);\n  React.useEffect(function () {\n    if (props.inert) {\n      document.body.classList.add(\"block-interactivity-\".concat(id));\n      var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n      allow_1.forEach(function (el) {\n        return el.classList.add(\"allow-interactivity-\".concat(id));\n      });\n      return function () {\n        document.body.classList.remove(\"block-interactivity-\".concat(id));\n        allow_1.forEach(function (el) {\n          return el.classList.remove(\"allow-interactivity-\".concat(id));\n        });\n      };\n    }\n    return;\n  }, [props.inert, props.lockRef.current, props.shards]);\n  var shouldCancelEvent = React.useCallback(function (event, parent) {\n    if ('touches' in event && event.touches.length === 2 || event.type === 'wheel' && event.ctrlKey) {\n      return !lastProps.current.allowPinchZoom;\n    }\n    var touch = getTouchXY(event);\n    var touchStart = touchStartRef.current;\n    var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n    var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n    var currentAxis;\n    var target = event.target;\n    var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n    // allow horizontal touch move on Range inputs. They will not cause any scroll\n    if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n      return false;\n    }\n    var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n    if (!canBeScrolledInMainDirection) {\n      return true;\n    }\n    if (canBeScrolledInMainDirection) {\n      currentAxis = moveDirection;\n    } else {\n      currentAxis = moveDirection === 'v' ? 'h' : 'v';\n      canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n      // other axis might be not scrollable\n    }\n    if (!canBeScrolledInMainDirection) {\n      return false;\n    }\n    if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n      activeAxis.current = currentAxis;\n    }\n    if (!currentAxis) {\n      return true;\n    }\n    var cancelingAxis = activeAxis.current || currentAxis;\n    return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n  }, []);\n  var shouldPrevent = React.useCallback(function (_event) {\n    var event = _event;\n    if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n      // not the last active\n      return;\n    }\n    var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n    var sourceEvent = shouldPreventQueue.current.filter(function (e) {\n      return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta);\n    })[0];\n    // self event, and should be canceled\n    if (sourceEvent && sourceEvent.should) {\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      return;\n    }\n    // outside or shard event\n    if (!sourceEvent) {\n      var shardNodes = (lastProps.current.shards || []).map(extractRef).filter(Boolean).filter(function (node) {\n        return node.contains(event.target);\n      });\n      var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n      if (shouldStop) {\n        if (event.cancelable) {\n          event.preventDefault();\n        }\n      }\n    }\n  }, []);\n  var shouldCancel = React.useCallback(function (name, delta, target, should) {\n    var event = {\n      name: name,\n      delta: delta,\n      target: target,\n      should: should,\n      shadowParent: getOutermostShadowParent(target)\n    };\n    shouldPreventQueue.current.push(event);\n    setTimeout(function () {\n      shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) {\n        return e !== event;\n      });\n    }, 1);\n  }, []);\n  var scrollTouchStart = React.useCallback(function (event) {\n    touchStartRef.current = getTouchXY(event);\n    activeAxis.current = undefined;\n  }, []);\n  var scrollWheel = React.useCallback(function (event) {\n    shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n  }, []);\n  var scrollTouchMove = React.useCallback(function (event) {\n    shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n  }, []);\n  React.useEffect(function () {\n    lockStack.push(Style);\n    props.setCallbacks({\n      onScrollCapture: scrollWheel,\n      onWheelCapture: scrollWheel,\n      onTouchMoveCapture: scrollTouchMove\n    });\n    document.addEventListener('wheel', shouldPrevent, nonPassive);\n    document.addEventListener('touchmove', shouldPrevent, nonPassive);\n    document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n    return function () {\n      lockStack = lockStack.filter(function (inst) {\n        return inst !== Style;\n      });\n      document.removeEventListener('wheel', shouldPrevent, nonPassive);\n      document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n      document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n    };\n  }, []);\n  var removeScrollBar = props.removeScrollBar,\n    inert = props.inert;\n  return React.createElement(React.Fragment, null, inert ? React.createElement(Style, {\n    styles: generateStyle(id)\n  }) : null, removeScrollBar ? React.createElement(RemoveScrollBar, {\n    noRelative: props.noRelative,\n    gapMode: props.gapMode\n  }) : null);\n}\nfunction getOutermostShadowParent(node) {\n  var shadowParent = null;\n  while (node !== null) {\n    if (node instanceof ShadowRoot) {\n      shadowParent = node.host;\n      node = node.host;\n    }\n    node = node.parentNode;\n  }\n  return shadowParent;\n}", "map": {"version": 3, "names": ["__spread<PERSON><PERSON>y", "React", "RemoveScrollBar", "styleSingleton", "nonPassive", "handleScroll", "locationCouldBeScrolled", "getTouchXY", "event", "changedTouches", "clientX", "clientY", "getDeltaXY", "deltaX", "deltaY", "extractRef", "ref", "current", "deltaCompare", "x", "y", "generateStyle", "id", "concat", "idCounter", "lockStack", "RemoveScrollSideCar", "props", "shouldPreventQueue", "useRef", "touchStartRef", "activeAxis", "useState", "Style", "lastProps", "useEffect", "inert", "document", "body", "classList", "add", "allow_1", "lockRef", "shards", "map", "filter", "Boolean", "for<PERSON>ach", "el", "remove", "shouldCancelEvent", "useCallback", "parent", "touches", "length", "type", "ctrl<PERSON>ey", "allowPinchZoom", "touch", "touchStart", "currentAxis", "target", "moveDirection", "Math", "abs", "canBeScrolledInMainDirection", "cancelingAxis", "shouldPrevent", "_event", "delta", "sourceEvent", "e", "name", "shadowParent", "should", "cancelable", "preventDefault", "shardNodes", "node", "contains", "shouldStop", "noIsolation", "shouldCancel", "getOutermostShadowParent", "push", "setTimeout", "scrollTouchStart", "undefined", "scrollWheel", "scrollTouchMove", "setCallbacks", "onScrollCapture", "onWheelCapture", "onTouchMoveCapture", "addEventListener", "inst", "removeEventListener", "removeScrollBar", "createElement", "Fragment", "styles", "noRelative", "gapMode", "ShadowRoot", "host", "parentNode"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/react-remove-scroll/dist/es2015/SideEffect.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,YAAY,EAAEC,uBAAuB,QAAQ,gBAAgB;AACtE,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACrC,OAAO,gBAAgB,IAAIA,KAAK,GAAG,CAACA,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAACC,OAAO,EAAEF,KAAK,CAACC,cAAc,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAClH,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUJ,KAAK,EAAE;EAAE,OAAO,CAACA,KAAK,CAACK,MAAM,EAAEL,KAAK,CAACM,MAAM,CAAC;AAAE,CAAC;AACjF,IAAIC,UAAU,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAC5B,OAAOA,GAAG,IAAI,SAAS,IAAIA,GAAG,GAAGA,GAAG,CAACC,OAAO,GAAGD,GAAG;AACtD,CAAC;AACD,IAAIE,YAAY,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC,IAAID,CAAC,CAAC,CAAC,CAAC,KAAKC,CAAC,CAAC,CAAC,CAAC;AAAE,CAAC;AAC7E,IAAIC,aAAa,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAAE,OAAO,2BAA2B,CAACC,MAAM,CAACD,EAAE,EAAE,mDAAmD,CAAC,CAACC,MAAM,CAACD,EAAE,EAAE,2BAA2B,CAAC;AAAE,CAAC;AACjL,IAAIE,SAAS,GAAG,CAAC;AACjB,IAAIC,SAAS,GAAG,EAAE;AAClB,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACvC,IAAIC,kBAAkB,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,EAAE,CAAC;EACzC,IAAIC,aAAa,GAAG7B,KAAK,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxC,IAAIE,UAAU,GAAG9B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EAC/B,IAAIP,EAAE,GAAGrB,KAAK,CAAC+B,QAAQ,CAACR,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;EACvC,IAAIS,KAAK,GAAGhC,KAAK,CAAC+B,QAAQ,CAAC7B,cAAc,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI+B,SAAS,GAAGjC,KAAK,CAAC4B,MAAM,CAACF,KAAK,CAAC;EACnC1B,KAAK,CAACkC,SAAS,CAAC,YAAY;IACxBD,SAAS,CAACjB,OAAO,GAAGU,KAAK;EAC7B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX1B,KAAK,CAACkC,SAAS,CAAC,YAAY;IACxB,IAAIR,KAAK,CAACS,KAAK,EAAE;MACbC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAACjB,MAAM,CAACD,EAAE,CAAC,CAAC;MAC9D,IAAImB,OAAO,GAAGzC,aAAa,CAAC,CAAC2B,KAAK,CAACe,OAAO,CAACzB,OAAO,CAAC,EAAE,CAACU,KAAK,CAACgB,MAAM,IAAI,EAAE,EAAEC,GAAG,CAAC7B,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC8B,MAAM,CAACC,OAAO,CAAC;MAChHL,OAAO,CAACM,OAAO,CAAC,UAAUC,EAAE,EAAE;QAAE,OAAOA,EAAE,CAACT,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAACjB,MAAM,CAACD,EAAE,CAAC,CAAC;MAAE,CAAC,CAAC;MAC9F,OAAO,YAAY;QACfe,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACU,MAAM,CAAC,sBAAsB,CAAC1B,MAAM,CAACD,EAAE,CAAC,CAAC;QACjEmB,OAAO,CAACM,OAAO,CAAC,UAAUC,EAAE,EAAE;UAAE,OAAOA,EAAE,CAACT,SAAS,CAACU,MAAM,CAAC,sBAAsB,CAAC1B,MAAM,CAACD,EAAE,CAAC,CAAC;QAAE,CAAC,CAAC;MACrG,CAAC;IACL;IACA;EACJ,CAAC,EAAE,CAACK,KAAK,CAACS,KAAK,EAAET,KAAK,CAACe,OAAO,CAACzB,OAAO,EAAEU,KAAK,CAACgB,MAAM,CAAC,CAAC;EACtD,IAAIO,iBAAiB,GAAGjD,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE4C,MAAM,EAAE;IAC/D,IAAK,SAAS,IAAI5C,KAAK,IAAIA,KAAK,CAAC6C,OAAO,CAACC,MAAM,KAAK,CAAC,IAAM9C,KAAK,CAAC+C,IAAI,KAAK,OAAO,IAAI/C,KAAK,CAACgD,OAAQ,EAAE;MACjG,OAAO,CAACtB,SAAS,CAACjB,OAAO,CAACwC,cAAc;IAC5C;IACA,IAAIC,KAAK,GAAGnD,UAAU,CAACC,KAAK,CAAC;IAC7B,IAAImD,UAAU,GAAG7B,aAAa,CAACb,OAAO;IACtC,IAAIJ,MAAM,GAAG,QAAQ,IAAIL,KAAK,GAAGA,KAAK,CAACK,MAAM,GAAG8C,UAAU,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;IACxE,IAAI5C,MAAM,GAAG,QAAQ,IAAIN,KAAK,GAAGA,KAAK,CAACM,MAAM,GAAG6C,UAAU,CAAC,CAAC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;IACxE,IAAIE,WAAW;IACf,IAAIC,MAAM,GAAGrD,KAAK,CAACqD,MAAM;IACzB,IAAIC,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACnD,MAAM,CAAC,GAAGkD,IAAI,CAACC,GAAG,CAAClD,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG;IACnE;IACA,IAAI,SAAS,IAAIN,KAAK,IAAIsD,aAAa,KAAK,GAAG,IAAID,MAAM,CAACN,IAAI,KAAK,OAAO,EAAE;MACxE,OAAO,KAAK;IAChB;IACA,IAAIU,4BAA4B,GAAG3D,uBAAuB,CAACwD,aAAa,EAAED,MAAM,CAAC;IACjF,IAAI,CAACI,4BAA4B,EAAE;MAC/B,OAAO,IAAI;IACf;IACA,IAAIA,4BAA4B,EAAE;MAC9BL,WAAW,GAAGE,aAAa;IAC/B,CAAC,MACI;MACDF,WAAW,GAAGE,aAAa,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;MAC/CG,4BAA4B,GAAG3D,uBAAuB,CAACwD,aAAa,EAAED,MAAM,CAAC;MAC7E;IACJ;IACA,IAAI,CAACI,4BAA4B,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAI,CAAClC,UAAU,CAACd,OAAO,IAAI,gBAAgB,IAAIT,KAAK,KAAKK,MAAM,IAAIC,MAAM,CAAC,EAAE;MACxEiB,UAAU,CAACd,OAAO,GAAG2C,WAAW;IACpC;IACA,IAAI,CAACA,WAAW,EAAE;MACd,OAAO,IAAI;IACf;IACA,IAAIM,aAAa,GAAGnC,UAAU,CAACd,OAAO,IAAI2C,WAAW;IACrD,OAAOvD,YAAY,CAAC6D,aAAa,EAAEd,MAAM,EAAE5C,KAAK,EAAE0D,aAAa,KAAK,GAAG,GAAGrD,MAAM,GAAGC,MAAM,EAAE,IAAI,CAAC;EACpG,CAAC,EAAE,EAAE,CAAC;EACN,IAAIqD,aAAa,GAAGlE,KAAK,CAACkD,WAAW,CAAC,UAAUiB,MAAM,EAAE;IACpD,IAAI5D,KAAK,GAAG4D,MAAM;IAClB,IAAI,CAAC3C,SAAS,CAAC6B,MAAM,IAAI7B,SAAS,CAACA,SAAS,CAAC6B,MAAM,GAAG,CAAC,CAAC,KAAKrB,KAAK,EAAE;MAChE;MACA;IACJ;IACA,IAAIoC,KAAK,GAAG,QAAQ,IAAI7D,KAAK,GAAGI,UAAU,CAACJ,KAAK,CAAC,GAAGD,UAAU,CAACC,KAAK,CAAC;IACrE,IAAI8D,WAAW,GAAG1C,kBAAkB,CAACX,OAAO,CAAC4B,MAAM,CAAC,UAAU0B,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACC,IAAI,KAAKhE,KAAK,CAAC+C,IAAI,KAAKgB,CAAC,CAACV,MAAM,KAAKrD,KAAK,CAACqD,MAAM,IAAIrD,KAAK,CAACqD,MAAM,KAAKU,CAAC,CAACE,YAAY,CAAC,IAAIvD,YAAY,CAACqD,CAAC,CAACF,KAAK,EAAEA,KAAK,CAAC;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxM;IACA,IAAIC,WAAW,IAAIA,WAAW,CAACI,MAAM,EAAE;MACnC,IAAIlE,KAAK,CAACmE,UAAU,EAAE;QAClBnE,KAAK,CAACoE,cAAc,CAAC,CAAC;MAC1B;MACA;IACJ;IACA;IACA,IAAI,CAACN,WAAW,EAAE;MACd,IAAIO,UAAU,GAAG,CAAC3C,SAAS,CAACjB,OAAO,CAAC0B,MAAM,IAAI,EAAE,EAC3CC,GAAG,CAAC7B,UAAU,CAAC,CACf8B,MAAM,CAACC,OAAO,CAAC,CACfD,MAAM,CAAC,UAAUiC,IAAI,EAAE;QAAE,OAAOA,IAAI,CAACC,QAAQ,CAACvE,KAAK,CAACqD,MAAM,CAAC;MAAE,CAAC,CAAC;MACpE,IAAImB,UAAU,GAAGH,UAAU,CAACvB,MAAM,GAAG,CAAC,GAAGJ,iBAAiB,CAAC1C,KAAK,EAAEqE,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC3C,SAAS,CAACjB,OAAO,CAACgE,WAAW;MACjH,IAAID,UAAU,EAAE;QACZ,IAAIxE,KAAK,CAACmE,UAAU,EAAE;UAClBnE,KAAK,CAACoE,cAAc,CAAC,CAAC;QAC1B;MACJ;IACJ;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIM,YAAY,GAAGjF,KAAK,CAACkD,WAAW,CAAC,UAAUqB,IAAI,EAAEH,KAAK,EAAER,MAAM,EAAEa,MAAM,EAAE;IACxE,IAAIlE,KAAK,GAAG;MAAEgE,IAAI,EAAEA,IAAI;MAAEH,KAAK,EAAEA,KAAK;MAAER,MAAM,EAAEA,MAAM;MAAEa,MAAM,EAAEA,MAAM;MAAED,YAAY,EAAEU,wBAAwB,CAACtB,MAAM;IAAE,CAAC;IACxHjC,kBAAkB,CAACX,OAAO,CAACmE,IAAI,CAAC5E,KAAK,CAAC;IACtC6E,UAAU,CAAC,YAAY;MACnBzD,kBAAkB,CAACX,OAAO,GAAGW,kBAAkB,CAACX,OAAO,CAAC4B,MAAM,CAAC,UAAU0B,CAAC,EAAE;QAAE,OAAOA,CAAC,KAAK/D,KAAK;MAAE,CAAC,CAAC;IACxG,CAAC,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,EAAE,CAAC;EACN,IAAI8E,gBAAgB,GAAGrF,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE;IACtDsB,aAAa,CAACb,OAAO,GAAGV,UAAU,CAACC,KAAK,CAAC;IACzCuB,UAAU,CAACd,OAAO,GAAGsE,SAAS;EAClC,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,WAAW,GAAGvF,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE;IACjD0E,YAAY,CAAC1E,KAAK,CAAC+C,IAAI,EAAE3C,UAAU,CAACJ,KAAK,CAAC,EAAEA,KAAK,CAACqD,MAAM,EAAEX,iBAAiB,CAAC1C,KAAK,EAAEmB,KAAK,CAACe,OAAO,CAACzB,OAAO,CAAC,CAAC;EAC9G,CAAC,EAAE,EAAE,CAAC;EACN,IAAIwE,eAAe,GAAGxF,KAAK,CAACkD,WAAW,CAAC,UAAU3C,KAAK,EAAE;IACrD0E,YAAY,CAAC1E,KAAK,CAAC+C,IAAI,EAAEhD,UAAU,CAACC,KAAK,CAAC,EAAEA,KAAK,CAACqD,MAAM,EAAEX,iBAAiB,CAAC1C,KAAK,EAAEmB,KAAK,CAACe,OAAO,CAACzB,OAAO,CAAC,CAAC;EAC9G,CAAC,EAAE,EAAE,CAAC;EACNhB,KAAK,CAACkC,SAAS,CAAC,YAAY;IACxBV,SAAS,CAAC2D,IAAI,CAACnD,KAAK,CAAC;IACrBN,KAAK,CAAC+D,YAAY,CAAC;MACfC,eAAe,EAAEH,WAAW;MAC5BI,cAAc,EAAEJ,WAAW;MAC3BK,kBAAkB,EAAEJ;IACxB,CAAC,CAAC;IACFpD,QAAQ,CAACyD,gBAAgB,CAAC,OAAO,EAAE3B,aAAa,EAAE/D,UAAU,CAAC;IAC7DiC,QAAQ,CAACyD,gBAAgB,CAAC,WAAW,EAAE3B,aAAa,EAAE/D,UAAU,CAAC;IACjEiC,QAAQ,CAACyD,gBAAgB,CAAC,YAAY,EAAER,gBAAgB,EAAElF,UAAU,CAAC;IACrE,OAAO,YAAY;MACfqB,SAAS,GAAGA,SAAS,CAACoB,MAAM,CAAC,UAAUkD,IAAI,EAAE;QAAE,OAAOA,IAAI,KAAK9D,KAAK;MAAE,CAAC,CAAC;MACxEI,QAAQ,CAAC2D,mBAAmB,CAAC,OAAO,EAAE7B,aAAa,EAAE/D,UAAU,CAAC;MAChEiC,QAAQ,CAAC2D,mBAAmB,CAAC,WAAW,EAAE7B,aAAa,EAAE/D,UAAU,CAAC;MACpEiC,QAAQ,CAAC2D,mBAAmB,CAAC,YAAY,EAAEV,gBAAgB,EAAElF,UAAU,CAAC;IAC5E,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,IAAI6F,eAAe,GAAGtE,KAAK,CAACsE,eAAe;IAAE7D,KAAK,GAAGT,KAAK,CAACS,KAAK;EAChE,OAAQnC,KAAK,CAACiG,aAAa,CAACjG,KAAK,CAACkG,QAAQ,EAAE,IAAI,EAC5C/D,KAAK,GAAGnC,KAAK,CAACiG,aAAa,CAACjE,KAAK,EAAE;IAAEmE,MAAM,EAAE/E,aAAa,CAACC,EAAE;EAAE,CAAC,CAAC,GAAG,IAAI,EACxE2E,eAAe,GAAGhG,KAAK,CAACiG,aAAa,CAAChG,eAAe,EAAE;IAAEmG,UAAU,EAAE1E,KAAK,CAAC0E,UAAU;IAAEC,OAAO,EAAE3E,KAAK,CAAC2E;EAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;AAChI;AACA,SAASnB,wBAAwBA,CAACL,IAAI,EAAE;EACpC,IAAIL,YAAY,GAAG,IAAI;EACvB,OAAOK,IAAI,KAAK,IAAI,EAAE;IAClB,IAAIA,IAAI,YAAYyB,UAAU,EAAE;MAC5B9B,YAAY,GAAGK,IAAI,CAAC0B,IAAI;MACxB1B,IAAI,GAAGA,IAAI,CAAC0B,IAAI;IACpB;IACA1B,IAAI,GAAGA,IAAI,CAAC2B,UAAU;EAC1B;EACA,OAAOhC,YAAY;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}