{"ast": null, "code": "/**\n * @license lucide-react v0.535.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 3v16a2 2 0 0 0 2 2h16\",\n  key: \"c24i48\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"13\",\n  width: \"9\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"1iip1u\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"5\",\n  width: \"12\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"1anskk\"\n}]];\nconst ChartBarBig = createLucideIcon(\"chart-bar-big\", __iconNode);\nexport { __iconNode, ChartBarBig as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "ChartBarBig", "createLucideIcon"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/lucide-react/src/icons/chart-bar-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 3v16a2 2 0 0 0 2 2h16', key: 'c24i48' }],\n  ['rect', { x: '7', y: '13', width: '9', height: '4', rx: '1', key: '1iip1u' }],\n  ['rect', { x: '7', y: '5', width: '12', height: '4', rx: '1', key: '1anskk' }],\n];\n\n/**\n * @component @name ChartBarBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzdjE2YTIgMiAwIDAgMCAyIDJoMTYiIC8+CiAgPHJlY3QgeD0iNyIgeT0iMTMiIHdpZHRoPSI5IiBoZWlnaHQ9IjQiIHJ4PSIxIiAvPgogIDxyZWN0IHg9IjciIHk9IjUiIHdpZHRoPSIxMiIgaGVpZ2h0PSI0IiByeD0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chart-bar-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChartBarBig = createLucideIcon('chart-bar-big', __iconNode);\n\nexport default ChartBarBig;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAMC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAMC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAU,GAC/E;AAaM,MAAAM,WAAA,GAAcC,gBAAiB,kBAAiBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}