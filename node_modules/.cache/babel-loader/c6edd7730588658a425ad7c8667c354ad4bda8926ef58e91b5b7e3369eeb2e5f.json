{"ast": null, "code": "import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n  var ref = useState(function () {\n    return {\n      // value\n      value: initialValue,\n      // last callback\n      callback: callback,\n      // \"memoized\" public interface\n      facade: {\n        get current() {\n          return ref.value;\n        },\n        set current(value) {\n          var last = ref.value;\n          if (last !== value) {\n            ref.value = value;\n            ref.callback(value, last);\n          }\n        }\n      }\n    };\n  })[0];\n  // update callback\n  ref.callback = callback;\n  return ref.facade;\n}", "map": {"version": 3, "names": ["useState", "useCallbackRef", "initialValue", "callback", "ref", "value", "facade", "current", "last"], "sources": ["/home/<USER>/Desktop/mostaql/odoo mohamed 8 hours/website/lucid/node_modules/use-callback-ref/dist/es2015/useRef.js"], "sourcesContent": ["import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,YAAY,EAAEC,QAAQ,EAAE;EACnD,IAAIC,GAAG,GAAGJ,QAAQ,CAAC,YAAY;IAAE,OAAQ;MACrC;MACAK,KAAK,EAAEH,YAAY;MACnB;MACAC,QAAQ,EAAEA,QAAQ;MAClB;MACAG,MAAM,EAAE;QACJ,IAAIC,OAAOA,CAAA,EAAG;UACV,OAAOH,GAAG,CAACC,KAAK;QACpB,CAAC;QACD,IAAIE,OAAOA,CAACF,KAAK,EAAE;UACf,IAAIG,IAAI,GAAGJ,GAAG,CAACC,KAAK;UACpB,IAAIG,IAAI,KAAKH,KAAK,EAAE;YAChBD,GAAG,CAACC,KAAK,GAAGA,KAAK;YACjBD,GAAG,CAACD,QAAQ,CAACE,KAAK,EAAEG,IAAI,CAAC;UAC7B;QACJ;MACJ;IACJ,CAAC;EAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACT;EACAJ,GAAG,CAACD,QAAQ,GAAGA,QAAQ;EACvB,OAAOC,GAAG,CAACE,MAAM;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}